package com.hll.mapdataservice.business.interceptor;

import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.extension.plugins.inner.InnerInterceptor;
import com.hll.mapdataservice.common.entity.Link;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.SystemMetaObject;

import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class CustomInnerInterceptor implements InnerInterceptor {
    @Override
    public void beforePrepare(StatementHandler sh, Connection connection, Integer transactionTimeout) {
        // do nothing
        MetaObject metaObject = SystemMetaObject.forObject(sh);
        MappedStatement ms = (MappedStatement) metaObject.getValue("delegate.mappedStatement");

        // check whether the table has geometry field
        boolean linkHasGeometry = false;
        boolean linkMHasGeometry = false;

        DatabaseMetaData metaData = null;
        try {
            metaData = connection.getMetaData();
            ResultSet columns = metaData.getColumns(null, null, "link", "geometry");
            if (columns.next()) {
                // 如果存在geometry字段，则不需要修改
                linkHasGeometry = true;
            }
            ResultSet columnsM = metaData.getColumns(null, null, "link_m", "geometry");
            if (columnsM.next()) {
                // 如果存在geometry字段，则不需要修改
                linkMHasGeometry = true;
            }
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }

//        String dbName;
//        try {
//            dbName = connection.getCatalog();
//        } catch (SQLException e) {
//            throw new RuntimeException(e);
//        }
//
//        Pattern pattern = Pattern.compile("hll_oversea_h_.*_(\\d{4}_q\\d)$");
//        Matcher matcher = pattern.matcher(dbName);
//
//        if (matcher.find()) {
//            String quarter = matcher.group(1); // 例如 2025_q1
//
//            String[] parts = quarter.split("_q");
//            int year = Integer.parseInt(parts[0]);
//            int q = Integer.parseInt(parts[1]);
//            // new version case
//            if ((year > 2025) || (2025 == year && q > 2) || "hll_oversea_h_mys_2025_q1".equals(dbName)){
//                hasGeometry = true;
//            }
//        }

        if("INSERT".equals(ms.getSqlCommandType().name())){
            // 在SQL语句执行前修改
            String originalSql = sh.getBoundSql().getSql();
            String area = MybatisPlusConfig.myTableName.get();
            String tableName = getTableName(originalSql, ms.getSqlCommandType().name(), area);
            String replaceString = tableName.replace(area, "");
            // System.out.println("beforeupdate originalSql：" + originalSql + "\narea is:" + area
            //         + "\ntableName is:" + tableName);
            if (!linkHasGeometry && originalSql.contains("geometry") && (originalSql.contains("insert into link_m")|| originalSql.contains("insert into node"))) {
                originalSql = originalSql.replace(",geometry", ",geom").replace(".geometry", ".geom");
            }
            if (!linkMHasGeometry && originalSql.contains("geometry") && (originalSql.contains("insert into link_m") || originalSql.contains("insert into node_m"))) {
                originalSql = originalSql.replace(",geometry", ",geom").replace(".geometry", ".geom");
            }
            String repaceSql = originalSql.replace(replaceString+".", tableName+".");
            // System.out.println("replaceSql：" + repaceSql);
            // 修改完成的sql 再设置回去
            metaObject.setValue("delegate.boundSql.sql", repaceSql);
        }
        if("SELECT".equals(ms.getSqlCommandType().name())){
            // 在SQL语句执行前修改
            String originalSql = sh.getBoundSql().getSql();
//            String area = MybatisPlusConfig.myTableName.get();
//            String tableName = getTableName(originalSql, ms.getSqlCommandType().name(), area);
//            String replaceString = tableName.replace(area, "");
//            // System.out.println("beforeupdate originalSql：" + originalSql + "\narea is:" + area
//            //         + "\ntableName is:" + tableName);
//            if (originalSql.contains("geometry") && (tableInfo.getTableName().equals("link") || tableInfo.getTableName().equals("node"))){
            if (!linkHasGeometry && originalSql.contains("geometry") && (originalSql.contains(" FROM link")|| originalSql.contains(" FROM node"))) {
                originalSql = originalSql.replace(",geometry,", ",geom as geometry,");
            }
            if (!linkMHasGeometry && originalSql.contains("geometry") && (originalSql.contains(" FROM link_m") || originalSql.contains(" FROM node_m"))) {
                originalSql = originalSql.replace(",geometry,", ",geom as geometry,");
            }
            // System.out.println("replaceSql：" + repaceSql);
            // 修改完成的sql 再设置回去
            metaObject.setValue("delegate.boundSql.sql", originalSql);
        }
    }
    public String getTableName(String originalSql, String commondType, String area){
        String tableName = "";
        if ("INSERT".equals(commondType) && area != null){
            tableName = originalSql.split(" ")[2];
        }
        return tableName;
    }
}
