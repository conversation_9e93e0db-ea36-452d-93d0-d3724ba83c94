package com.hll.mapdataservice.business.api.road.service;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.google.common.collect.Lists;
import com.hll.mapdataservice.business.common.GroupManager;
import com.hll.mapdataservice.business.interceptor.MybatisPlusConfig;
import com.hll.mapdataservice.business.third.InheritIDService;
import com.hll.mapdataservice.business.third.dto.InheritIDDTO;
import com.hll.mapdataservice.common.entity.*;
import com.hll.mapdataservice.common.mapper.ZlevelMMapper;
import com.hll.mapdataservice.common.service.IZlevelMService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hll.mapdataservice.common.utils.CommonUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@Service
@Slf4j
public class ZlevelMServiceImpl extends ServiceImpl<ZlevelMMapper, ZlevelM> implements IZlevelMService {

    @Resource
    private ZlevelMMapper zlevelMMapper;
    @Resource
    private InheritIDService inheritIDService;
    @Resource
    private ZlevelsServiceImpl zlevelsService;


    @Async("asyncTaskExecutor")
    public void zlevelMConvert(List<Zlevels> zlevelsList, String area,
                               String country, CountDownLatch countDownLatch, GroupManager groupManager) throws SQLException, InterruptedException {

        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
            log.info("processing country:" + CommonUtils.getDsbyCountry(country, true));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        log.info("tmp thread is:" + Thread.currentThread().getName());
        List<ZlevelM> zlevelMList = new ArrayList<>();

        try {
            if (zlevelsList.size() > 0) {
                for (Zlevels zlevels : zlevelsList
                ) {
                    ZlevelM zlevelM = new ZlevelM();

                    zlevelM.setZlevelId(zlevels.getGid().toString());
                    zlevelM.setHllLinkid(zlevels.getLinkId().toString());
                    zlevelM.setPointNum(zlevels.getPointNum());
                    zlevelM.setHllNodeid(zlevels.getNodeId().toString());
                    zlevelM.setGeom(zlevels.getGeom());
                    zlevelM.setZ(zlevels.getzLevel().toString());
                    zlevelM.setIntrsect(zlevels.getIntrsect());
                    zlevelM.setDotShape(zlevels.getDotShape());
                    zlevelM.setAligned(zlevels.getAligned());
                    zlevelM.setLinktype("1");
                    zlevelM.setTileId(null);
                    zlevelM.setTileType(null);
                    zlevelM.setDatasource("here");
                    zlevelM.setUpDate(LocalDateTime.now());
                    zlevelM.setStatus(0);
                    zlevelM.setLevelId(groupManager.getGroupId(zlevels.getGid()) == null ? null : groupManager.getGroupId(zlevels.getGid()).toString());

                    zlevelMList.add(zlevelM);
                }
                log.info("zlevellist size is:" + zlevelMList.size());
                // this.saveOrUpdateBatch(linList);
                List<List<ZlevelM>> zlevelMListPartition = Lists.partition(zlevelMList, 32767 / BeanUtil.beanToMap(new ZlevelM()).keySet().size());
                if (!country.isEmpty()) {
                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                }
                if (!area.isEmpty()) {
                    MybatisPlusConfig.myTableName.set("_" + area);
                } else {
                    MybatisPlusConfig.myTableName.set("");
                }

                for (List<ZlevelM> partitionList : zlevelMListPartition) {
                    //批量处理ID
                    List<Long> linkIds = inheritIDService.inheritID(new InheritIDDTO(12L, partitionList.stream().map(ZlevelM::getHllLinkid).collect(Collectors.toList())));
                    // 收集需要处理的nodeIds
                    List<String> nodeIdsToProcess = partitionList.stream()
                            .filter(z -> !"0".equals(z.getHllNodeid()))
                            .map(ZlevelM::getHllNodeid)
                            .collect(Collectors.toList());
                    // 只处理非0的nodeIds
                    List<Long> processedNodeIds = !nodeIdsToProcess.isEmpty() ?
                            inheritIDService.inheritID(new InheritIDDTO(12L, nodeIdsToProcess)) :
                            Collections.emptyList();

//                    List<Long> nodeIds = inheritIDService.inheritID(new InheritIDDTO(12L, partitionList.stream().map(Zlevel::getHllNodeid).collect(Collectors.toList())));
                    Iterator<Long> nodeIdIter = processedNodeIds.iterator();
                    for (int i = 0; i < partitionList.size(); i++) {
                        partitionList.get(i).setHllLinkid(String.valueOf(linkIds.get(i)));
                        if (!"0".equals(partitionList.get(i).getHllNodeid()) && nodeIdIter.hasNext()) {
                            partitionList.get(i).setHllNodeid(String.valueOf(nodeIdIter.next()));
                        }
                    }
//                    //批量处理ID
//                    List<Long> linkIds = inheritIDService.inheritID(new InheritIDDTO(12L, partitionList.stream().map(ZlevelM::getHllLinkid).collect(Collectors.toList())));
//                    List<Long> nodeIds = inheritIDService.inheritID(new InheritIDDTO(12L, partitionList.stream().map(ZlevelM::getHllNodeid).collect(Collectors.toList())));
//                    for (int i = 0; i < partitionList.size(); i++) {
//                        partitionList.get(i).setHllLinkid(String.valueOf(linkIds.get(i)));
//                        partitionList.get(i).setHllNodeid(String.valueOf(nodeIds.get(i)));
//                    }
                    zlevelMMapper.mysqlInsertOrUpdateBath(partitionList);
                }
            }
        } catch (Exception e) {
            log.error("zlevel convert error,msg is {}", e);
            throw new RuntimeException("zlevel convert error,msg is {}" + e.getMessage());
        } finally {
            countDownLatch.countDown();
        }
    }
}
