package com.hll.mapdataservice.business.api.common;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.smallbun.screw.core.Configuration;
import cn.smallbun.screw.core.engine.EngineConfig;
import cn.smallbun.screw.core.engine.EngineFileType;
import cn.smallbun.screw.core.engine.EngineTemplateType;
import cn.smallbun.screw.core.exception.BuilderException;
import cn.smallbun.screw.core.execute.DocumentationExecute;
import cn.smallbun.screw.core.process.ProcessConfig;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.hll.mapdataservice.business.api.road.service.StreetsServiceImpl;
import com.hll.mapdataservice.business.interceptor.MybatisPlusConfig;
import com.hll.mapdataservice.business.entity.DbExport;
import com.hll.mapdataservice.common.ResponseResult;
import com.hll.mapdataservice.common.entity.LineDO;
import com.hll.mapdataservice.common.entity.Streets;
import com.hll.mapdataservice.common.mapper.StreetsMapper;
import com.hll.mapdataservice.common.utils.CommonUtils;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

import javax.sql.DataSource;

/**
 * @Author: ares.chen
 * @Since: 2021/12/21
 */
@RestController
@RequestMapping("common")
@Slf4j
public class CommonController {

    @Resource
    private StreetsMapper streetsMapper;

    @Resource
    private StreetsServiceImpl streetsService;

    @GetMapping("nameSimilarity")
    public ResponseResult<Double> nameSimilarity(String name1, String name2) {
        float levenshtein = CommonUtils.levenshtein(name1, name2);
        return ResponseResult.OK(Double.valueOf(levenshtein), true);
    }

    @GetMapping("reverseLine")
    public ResponseResult<Boolean> reverseLineXY(String srcFilePath, String destFilePath) {
        List<LineDO> lineDOList = new ArrayList<>();
        EasyExcel.read(srcFilePath, LineDO.class, new PageReadListener(data -> {
            lineDOList.addAll((Collection<? extends LineDO>) data);
        })).sheet().doRead();
        lineDOList.removeIf(s -> StrUtil.isEmpty(s.getLine()));

        List<LineDO> lineList = new ArrayList<>();
        // 反转点的坐标
        for (LineDO lineDO : lineDOList) {
            String line = lineDO.getLine();
            String[] coords = line.split(";");
            StringBuilder sb = new StringBuilder();
            for (String coord : coords) {
                String[] xy = coord.split(",");
                String temp = xy[0];
                xy[0] = xy[1];
                xy[1] = temp;
                sb.append(xy[0]).append(",").append(xy[1]).append(";");
            }
            lineList.add(new LineDO(StrUtil.removeSuffix(sb, ";")));
        }

        String fileName = StrUtil.sub(srcFilePath, srcFilePath.lastIndexOf("/") + 1, srcFilePath.indexOf("."));
        destFilePath = destFilePath.endsWith("/") ? destFilePath + fileName + "_reverse_" + System.currentTimeMillis() + ".xlsx" :
                destFilePath + "/" + fileName + "_reverse_" + System.currentTimeMillis() + ".xlsx";
        EasyExcel.write(destFilePath, LineDO.class).sheet().doWrite(lineList);

        return ResponseResult.OK(true, true);
    }

    @GetMapping("reverseLine2")
    public ResponseResult<String> reverseLine(String linkIds) {
        String[] links = linkIds.split(",");
        List<String> linkList = Arrays.asList(links);
        List<String> reverseLines = CollUtil.reverse(linkList);
        ResponseResult<String> res = new ResponseResult<>();
        res.setData(CollUtil.join(reverseLines, ","));
        res.setSuccess(true);
        return res;
    }
    @GetMapping("dbbackup")
    public ResponseResult<Boolean> dbbackup(@RequestParam("dbName") String dbName,
                                                  @RequestParam("userName") String userName,
                                                  @RequestParam("host") String host,
                                                  @RequestParam("port") String port,
                                                  @RequestParam("backupPath") String backupPath,
                                                  @RequestParam("dbPath") String dbPath) {
        return ResponseResult.OK(CommonUtils.backupDB(dbName,userName,host,port,backupPath,dbPath), true);
    }

    @GetMapping("minioupload")
    public ResponseResult<Boolean> minioupload(@RequestParam("endPoint") String endPoint,
                                            @RequestParam("accessKey") String accessKey,
                                            @RequestParam("secretKey") String secretKey,
                                            @RequestParam("bucketName") String bucketName,
                                            @RequestParam("objectName") String objectName,
                                            @RequestParam("filePath") String filePath) {
        return ResponseResult.OK(CommonUtils.minioUpload(endPoint,accessKey,secretKey,bucketName,objectName,filePath), true);
    }

    @GetMapping("repeatLine")
    public ResponseResult<String> findRepeatLine() {
        List<String> countries = CollUtil.newArrayList("bra","hkg","idn","mex","mys","phl","sgp","tha","twn","vnm");
        List<Long> allLinkIds = new ArrayList<>();
        for (String country : countries) {
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
            }
            MybatisPlusConfig.myTableName.set("");
            List<Long> linkIds = streetsService.lambdaQuery().select(Streets::getLinkId).list().stream().map(Streets::getLinkId).collect(Collectors.toList());
            log.info("country:{}, linkIds size:{}", country, linkIds.size());
            allLinkIds.addAll(linkIds);
            log.info("allLinkIds size:{}", allLinkIds.size());
        }
        List<Long> collect = allLinkIds.stream().distinct().collect(Collectors.toList());
        log.info("allLinkIds size is:{},after remove repeated linkIds size is:{}",allLinkIds.size(), collect.size());
        return allLinkIds.size()!= collect.size()?ResponseResult.OK("有重复",true):ResponseResult.OK("无重复",true);
    }

    /**
     * 导出指定数据库表信息为html,word,md
     *
     * @param dbExport
     * @return
     */
    @PostMapping("exportDbDoc")
    public ResponseResult<String> exportDbDoc(@RequestBody DbExport dbExport) {
        try {
            // 数据源
            HikariConfig hikariConfig = new HikariConfig();
            hikariConfig.setDriverClassName("org.postgresql.Driver");
            hikariConfig.setJdbcUrl("jdbc:postgresql://"+dbExport.getUrl()+":"+dbExport.getPort()+"/"+dbExport.getDb());
            hikariConfig.setUsername(dbExport.getUser());
            hikariConfig.setPassword(dbExport.getPwd());
            // 设置可以获取tables remarks信息
            hikariConfig.addDataSourceProperty("useInformationSchema", "true");
            hikariConfig.setMinimumIdle(2);
            hikariConfig.setMaximumPoolSize(5);
            DataSource dataSource = new HikariDataSource(hikariConfig);
            EngineFileType engineFileType = null;
            switch (dbExport.getExportType()) {
                case "1":
                    engineFileType = EngineFileType.HTML;
                    break;
                case "2":
                    engineFileType = EngineFileType.WORD;
                    break;
                case "3":
                    engineFileType = EngineFileType.MD;
                    break;
            }
            // 生成配置
            // fileOutputDir = "/Users/<USER>";
            EngineConfig engineConfig = EngineConfig.builder()
                    // 生成文件路径
                    .fileOutputDir(dbExport.getFileOutputDir())
                    // 打开目录
                    .openOutputDir(true)
                    // 文件类型
                    .fileType(engineFileType)
                    // 生成模板实现
                    .produceType(EngineTemplateType.freemarker)
                    // 自定义文件名称
                    .fileName(dbExport.getFileName()).build();

            // 忽略表
            // ArrayList<String> ignoreTableName = new ArrayList<>();
            // ignoreTableName.add("order_info");
            // ignoreTableName.add("test_group");
            // 忽略表前缀
            // ArrayList<String> ignorePrefix = new ArrayList<>();
            // ignorePrefix.add("test_");
            // 忽略表后缀
            // ArrayList<String> ignoreSuffix = new ArrayList<>();
            // ignoreSuffix.add("_src");
            ProcessConfig processConfig = ProcessConfig.builder()
                    // 指定生成逻辑、当存在指定表、指定表前缀、指定表后缀时，将生成指定表，其余表不生成、并跳过忽略表配置
                    // 根据名称指定表生成
                    .designatedTableName(new ArrayList<>())
                    // 根据表前缀生成
                    .designatedTablePrefix(new ArrayList<>())
                    // 根据表后缀生成
                    .designatedTableSuffix(new ArrayList<>())
                    // 忽略表名
                    .ignoreTableName(dbExport.getIgnoreTables())
                    // 忽略表前缀
                    .ignoreTablePrefix(dbExport.getIgnorePrefixes())
                    // 忽略表后缀
                    .ignoreTableSuffix(dbExport.getIgnoreSuffixes()).build();
            // 配置
            Configuration config = Configuration.builder()
                    // 版本
                    .version(dbExport.getVersion())
                    // 描述
                    .description(dbExport.getFileDesc())
                    // 数据源
                    .dataSource(dataSource)
                    // 生成配置
                    .engineConfig(engineConfig)
                    // 生成配置
                    .produceConfig(processConfig)
                    .build();
            // 执行生成
            new DocumentationExecute(config).execute();
        } catch (BuilderException e) {
            log.error("导出数据库文档失败：{}",e.getMessage());
            throw new RuntimeException(e);
        }
        return ResponseResult.OK("导出路径为："+dbExport.getFileOutputDir()+",导出文件名："+dbExport.getFileName(),true);
    }
}