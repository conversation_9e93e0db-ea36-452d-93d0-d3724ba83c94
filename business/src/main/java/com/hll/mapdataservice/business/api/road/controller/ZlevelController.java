package com.hll.mapdataservice.business.api.road.controller;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.hll.mapdataservice.business.api.road.service.ZlevelMServiceImpl;
import com.hll.mapdataservice.business.api.road.service.ZlevelServiceImpl;
import com.hll.mapdataservice.business.api.road.service.ZlevelsServiceImpl;
import com.hll.mapdataservice.business.common.GroupManager;
import com.hll.mapdataservice.business.interceptor.MybatisPlusConfig;
import com.hll.mapdataservice.common.ResponseResult;
import com.hll.mapdataservice.common.entity.LinkM;
import com.hll.mapdataservice.common.entity.ZlevelM;
import com.hll.mapdataservice.common.entity.Zlevels;
import com.hll.mapdataservice.common.mapper.ZlevelsMapper;
import com.hll.mapdataservice.common.utils.CommonUtils;
import com.vividsolutions.jts.io.ParseException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;
import java.sql.SQLException;
import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@RestController
@ResponseBody
@Api(tags = "road")
@Component
@Slf4j
@RequestMapping("/api/road/zlevel")
public class ZlevelController {
    @Resource
    private ZlevelsServiceImpl zlevelsService;
    @Resource
    private ZlevelsMapper zlevelsMapper;

    @Resource
    private ZlevelMServiceImpl zlevelMService;
    @Resource
    private ZlevelServiceImpl zlevelService;

    @ApiOperation("map zlevel_m diff column to zlevel")
    @GetMapping("convert2rp")
    public ResponseResult<Boolean> convert2rp(@RequestParam(value = "step", required = false, defaultValue = "1") int step,
                                              @RequestParam(value = "area", required = false, defaultValue = "") String area,
                                              @RequestParam(value = "country", required = false, defaultValue = "") String country) throws InterruptedException {
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        TimeInterval timer = DateUtil.timer();
        // calculate loop times
        int zlevelMCount = zlevelMService.count();
        int loop = zlevelMCount % step != 0 ? (zlevelMCount / step) + 1 : zlevelMCount / step;
        CountDownLatch countDownLatch = new CountDownLatch(loop);
        for (int i = 0; i < loop; i++) {
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            List<ZlevelM> zlevelMList = zlevelMService.lambdaQuery().ne(ZlevelM::getStatus, 1)
                    .orderByDesc(ZlevelM::getZlevelId).last("limit " + step + " offset " + i * step).list();
            zlevelService.convert2rp(area, country, countDownLatch, zlevelMList);
        }
        countDownLatch.await();
        log.info("map zlevel_m diff column to zlevel cost time is {}s", timer.intervalSecond());
        return ResponseResult.OK(true, true);
    }
}
