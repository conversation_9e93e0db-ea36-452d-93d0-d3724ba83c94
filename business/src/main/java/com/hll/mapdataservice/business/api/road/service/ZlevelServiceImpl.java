package com.hll.mapdataservice.business.api.road.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.ListUtil;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hll.mapdataservice.business.common.GroupManager;
import com.hll.mapdataservice.business.interceptor.MybatisPlusConfig;
import com.hll.mapdataservice.business.third.InheritIDService;
import com.hll.mapdataservice.common.entity.ZlevelM;
import com.hll.mapdataservice.common.entity.Zlevel;
import com.hll.mapdataservice.common.entity.ZlevelM;
import com.hll.mapdataservice.common.entity.Zlevels;
import com.hll.mapdataservice.common.mapper.ZlevelMMapper;
import com.hll.mapdataservice.common.mapper.ZlevelMapper;
import com.hll.mapdataservice.common.service.IZlevelService;
import com.hll.mapdataservice.common.utils.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;

@Service
@Slf4j
public class ZlevelServiceImpl extends ServiceImpl<ZlevelMapper, Zlevel> implements IZlevelService {

    @Resource
    private ZlevelMapper zlevelMapper;

    @Async("asyncTaskExecutor")
    public void convert2rp(String area, String country, CountDownLatch countDownLatch, List<ZlevelM> zlevelMList) {
        try {
            log.info("area is {},country is{},db is{}", area, country, DynamicDataSourceContextHolder.peek());
            List<Zlevel> resList = new ArrayList<>();
            for (ZlevelM zlevelM : zlevelMList) {
                Zlevel zlevel = new Zlevel();
                BeanUtil.copyProperties(zlevelM, zlevel);
                zlevel.setId(Long.valueOf(zlevelM.getZlevelId()));
                zlevel.setGeometry(zlevelM.getGeom());
                //差异字段对应赋值
                resList.add(zlevel);
            }

            int fieldNum = BeanUtil.beanToMap(new Zlevel()).keySet().size();
            int batchSize = 32767 / fieldNum;
            log.info("batchInsert size is {}", batchSize);
            List<List<Zlevel>> splitList = ListUtil.split(resList, batchSize);
            for (List<Zlevel> zlevels : splitList) {
                if (!country.isEmpty()) {
                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                }
                if (!area.isEmpty()) {
                    MybatisPlusConfig.myTableName.set("_" + area);
                } else {
                    MybatisPlusConfig.myTableName.set("");
                }
                zlevelMapper.mysqlInsertOrUpdateBath(zlevels);
            }
        } catch (Exception e) {
            log.error("sync to link_rp error,detail is {}", e.getMessage());
            throw e;
        } finally {
            countDownLatch.countDown();
        }
    }
}
