package com.hll.mapdataservice.business.api.road.controller;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.hll.mapdataservice.business.api.road.service.ZlevelMServiceImpl;
import com.hll.mapdataservice.business.api.road.service.ZlevelsServiceImpl;
import com.hll.mapdataservice.business.common.GroupManager;
import com.hll.mapdataservice.business.interceptor.MybatisPlusConfig;
import com.hll.mapdataservice.common.ResponseResult;
import com.hll.mapdataservice.common.entity.Zlevels;
import com.hll.mapdataservice.common.mapper.ZlevelsMapper;
import com.hll.mapdataservice.common.utils.CommonUtils;
import com.vividsolutions.jts.io.ParseException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.CountDownLatch;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@RestController
@ResponseBody
@Api(tags = "road")
@Component
@Slf4j
@RequestMapping("/api/road/zlevelM")
public class ZlevelMController {
    @Resource
    private ZlevelsServiceImpl zlevelsService;
    @Resource
    private ZlevelMServiceImpl zlevelMServiceImpl;
    @Resource
    private ZlevelsMapper zlevelsMapper;

    @ApiOperation(value = "here zlevel convert")
    @PostMapping("/convert")
    public ResponseResult<Boolean> hereZlevelConvert(@RequestParam(value = "step",
                                                           required = false,
                                                           defaultValue = "1") int step,
                                                   @RequestParam(value = "version",
                                                           required = false) String version,
                                                   @RequestParam(value = "area",
                                                           required = false,
                                                           defaultValue = "") String area,
                                                   @RequestParam(value = "country",
                                                           required = false,
                                                           defaultValue = "") String country)
            throws SQLException, ParseException, InterruptedException, IOException, NoSuchFieldException {

        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        TimeInterval timer = DateUtil.timer();

        int listSize = zlevelsService.count();
        CountDownLatch countDownLatch = new CountDownLatch(listSize / step + 1);
        log.info("The records to be transfered:" + listSize);


        GroupManager groupManager = new GroupManager();
        List<Integer[]> duplicateGeomGroups = zlevelsService.findDuplicateGeomGroups();

        if (!duplicateGeomGroups.isEmpty()) {
            log.info("The duplicate geom groups size is:{}", duplicateGeomGroups.size());
            groupManager.initGroups(duplicateGeomGroups);
        } else {
            log.info("There is no duplicate geom groups");
        }

        for (int i = 0; i <= listSize / step; i++) {
//            if (i == 0) {
//                step = 100;
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            List<Zlevels> zlevelsList = zlevelsService.lambdaQuery().orderByAsc(Zlevels::getGid)
                    .last("limit " + step + " offset " + i * step).list();
            log.info("process start limit " + step + " offset " + i * step);
            if (!zlevelsList.isEmpty()) {
                zlevelMServiceImpl.zlevelMConvert(zlevelsList, area, country, countDownLatch ,groupManager);
            }
//            }
        }
        countDownLatch.await();
        log.info("here zlevel convert cost time is {}s", timer.intervalSecond());
        return ResponseResult.OK(true, true);
    }
}
