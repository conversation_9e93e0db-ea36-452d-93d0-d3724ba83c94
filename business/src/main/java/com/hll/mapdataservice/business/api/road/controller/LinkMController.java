package com.hll.mapdataservice.business.api.road.controller;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.map.BiMap;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.carrotsearch.hppc.LongArrayList;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.carrotsearch.hppc.cursors.LongCursor;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Multimap;
import com.graphhopper.util.Helper;
import com.hll.mapdataservice.business.api.poi.service.MtdareaServiceImpl;
import com.hll.mapdataservice.business.api.poi.service.PlanetOsmLineServiceImpl;
import com.hll.mapdataservice.business.api.road.service.*;
import com.hll.mapdataservice.business.base.MergeUtil;
import com.hll.mapdataservice.business.config.MapTTConfig;
import com.hll.mapdataservice.business.interceptor.MybatisPlusConfig;
import com.hll.mapdataservice.business.third.InheritIDService;
import com.hll.mapdataservice.business.third.dto.InheritIDDTO;
import com.hll.mapdataservice.common.GlobalCodeEnum;
import com.hll.mapdataservice.common.ResponseResult;
import com.hll.mapdataservice.common.constant.Const;
import com.hll.mapdataservice.common.entity.*;
import com.hll.mapdataservice.common.mapper.*;
import com.hll.mapdataservice.common.service.ILinkBreakService;
import com.hll.mapdataservice.common.utils.CommonUtils;
import com.hll.mapdataservice.business.common.XmlFileUtils;
import com.hll.mapdataservice.business.common.OptimizedXmlFileUtils;
import com.hll.mapdataservice.common.utils.osm.*;
import com.vividsolutions.jts.io.ParseException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.locationtech.jts.io.WKBReader;
import org.locationtech.jts.io.WKTWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.xml.stream.XMLStreamException;
import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;
import java.util.Optional;
import java.util.AbstractMap.SimpleEntry;
import java.util.Map.Entry;

import static com.graphhopper.util.Helper.nf;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-11
 */
@RestController
@ResponseBody
@Api(tags = "road")
@Component
@Slf4j
@RequestMapping("/api/road/herelink")
public class LinkMController {

    @Resource
    LinkSw2021q133Mapper linkSw2021q133Mapper;
    @Resource
    LinkMServiceImpl linkMService;
    @Resource
    LinkMMapper linkMMapper;
    @Resource
    StreetsServiceImpl streetsService;
    @Resource
    StreetsMapper streetsMapper;
    @Resource
    NodeMServiceImpl nodeMService;
    @Resource
    NodeMMapper nodeMMapper;
    @Resource
    RuleSw2021q133Mapper ruleSw2021q133Mapper;
    @Resource
    RuleSw2021q133ServiceImpl ruleSw2021q133Service;
    @Resource
    RuleMServiceImpl ruleService;
    @Resource
    RelationMServiceImpl relationMService;
    @Resource
    LinkServiceImpl linkService;
    @Resource
    NodeServiceImpl nodeService;
    @Resource
    RoadMapper roadMapper;
    @Resource
    RoadServiceImpl roadService;
    @Resource
    RoadMatchResServiceImpl roadMatchResService;
    @Resource
    CalculationMapper calculationMapper;
    @Resource
    RoadBreakServiceImpl roadBreakService;
    @Resource
    RoadBreakMapper roadBreakMapper;

    @Resource
    InheritIDService inheritIDService;

    @Resource
    ILinkBreakService linkBreakService;

    @Resource
    OptimizedLinkMService optimizedLinkMService;

    @Resource
    RuleMMapper ruleMapper;
    @Resource
    MtddstServiceImpl mtddstService;
    @Resource
    MtdareaServiceImpl mtdareaService;
    @Resource
    PlanetOsmLineServiceImpl planetOsmLineService;
    @Resource
    MapTTConfig mapTTConfig;
    @Autowired
    private LinkMapper linkMapper;
    @Autowired
    private RelationSw2021q133ServiceImpl relationSw2021q133ServiceImpl;

    @GetMapping("/mock1")
    public ResponseResult<Boolean> mock1() {
        try {
            TimeInterval timer = DateUtil.timer();
            Thread.currentThread().sleep(1000);
            log.info("mock1 cost time is {}s", timer.intervalSecond());
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        return ResponseResult.OK(true, true);
    }


    @GetMapping("/mock2")
    public ResponseResult<Boolean> mock2() {
        try {
            TimeInterval timer = DateUtil.timer();
            Thread.currentThread().sleep(2000);
            log.info("mock2 cost time is {}s", timer.intervalSecond());
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        return ResponseResult.OK(true, true);
    }

    @ApiOperation(value = "here link convert")
    @PostMapping("/convert")
    public ResponseResult<Boolean> hereLinkConvert(@RequestParam(value = "step",
                                                           required = false,
                                                           defaultValue = "1") int step,
                                                   @RequestParam(value = "version",
                                                           required = false) String version,
                                                   @RequestParam(value = "rdfcflinkfilepath",
                                                           required = true) String rdfCfLinkFilePath,
                                                   @RequestParam(value = "rdfcffilepath",
                                                           required = true) String rdfCfFilePath,
                                                   @RequestParam(value = "rdfcfnodefilepath",
                                                           required = true) String rdfCfNodeFilePath,
                                                   @RequestParam(value = "rdfnavlinkfilepath",
                                                           required = true) String rdfNavLinkFilePath,
                                                   @RequestParam(value = "iscompilenode",
                                                           required = false,
                                                           defaultValue = "true") boolean isCompileNode,
                                                   @RequestParam(value = "iscompiletranseng",
                                                           required = false,
                                                           defaultValue = "false") boolean isCompileTransEng,
                                                   @RequestParam(value = "area",
                                                           required = false,
                                                           defaultValue = "") String area,
                                                   @RequestParam(value = "country",
                                                           required = false,
                                                           defaultValue = "") String country)
            throws SQLException, ParseException, InterruptedException, IOException, NoSuchFieldException {

        // MybatisPlusConfig.zLevelsTableName.set("zlevels"+proFileEntity.getName());
        // MybatisPlusConfig.zLevelsTableName.set("zlevels_area1");
        // MybatisPlusConfig.myTableName.set("_"+area);
        // System.err.println("yyy:" + Thread.currentThread().getName());
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        TimeInterval timer = DateUtil.timer();

        // MybatisPlusConfig.myTableName ="_"+area;
        // MybatisPlusConfig.datasource ="4";

        // dataSourceInfo.setDatasource("db4");

        // readRdfCf
//        Map<String, String> rdfCfMap = new HashMap<>();
//        MultiValueMap<String, String> rdfLinkCfMap = new LinkedMultiValueMap<>();
//        Map<String, String> rdfNavLinkMap = new HashMap<>();
        Map<String, String> rdfCfMap = new XmlFileUtils().rdfCfFileRead(rdfCfFilePath);
        log.info("read readRdfCf finished,size is:" + rdfCfMap.size());
        // readRdfCfLink
        MultiValueMap<String, String> rdfLinkCfMap = new XmlFileUtils().rdfLinkCfFileRead(rdfCfLinkFilePath);
        log.info("read rdfCfLink finished,size is:" + rdfLinkCfMap.size());
        Map<String, List<String>> rdfNavLinkMap = new XmlFileUtils().rdfNavLinkFileRead(rdfNavLinkFilePath, country);
        log.info("read readRdfNav finished,size is:" + rdfNavLinkMap.size());

        Set<Integer> nodeIdSet = Collections.synchronizedSet(new TreeSet<>());
        List<NodeM> nodeList = Collections.synchronizedList(new ArrayList<>());

        Integer listSize = streetsService.count();
        CountDownLatch countDownLatch = new CountDownLatch(listSize / step + 1);
        log.info("The records to be transfered:" + listSize);
        for (int i = 0; i <= listSize / step; i++) {
//            if (i == 0) {
//                step = 100;
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            List<Streets> streetsListi = streetsService.lambdaQuery()
                    .orderByDesc(Streets::getLinkId).last("limit " + step + " offset " + i * step).list();
            log.info("process start limit " + step + " offset " + i * step);
            if (streetsListi.size() > 0) {
                // linkSw2021q133Service.linkConvert(streetsListi,nodeIdSet,nodeList);
                linkMService.linkConvert(streetsListi, nodeIdSet, rdfLinkCfMap, rdfCfMap, rdfNavLinkMap, isCompileNode,
                        isCompileTransEng, area, country, countDownLatch);
            }
//            }
        }
        countDownLatch.await();
        log.info("here streets convert to link node cost time is {}s", timer.intervalSecond());
        return ResponseResult.OK(true, true);
    }

    @ApiOperation(value = "here link convert optimized - NEW OPTIMIZED VERSION")
    @PostMapping("/convert-optimized")
    public ResponseResult<Boolean> hereLinkConvertOptimized(@RequestParam(value = "step",
                                                                    required = false,
                                                                    defaultValue = "0") int step,
                                                            @RequestParam(value = "version",
                                                                    required = false) String version,
                                                            @RequestParam(value = "rdfcflinkfilepath",
                                                                    required = true) String rdfCfLinkFilePath,
                                                            @RequestParam(value = "rdfcffilepath",
                                                                    required = true) String rdfCfFilePath,
                                                            @RequestParam(value = "rdfcfnodefilepath",
                                                                    required = true) String rdfCfNodeFilePath,
                                                            @RequestParam(value = "rdfnavlinkfilepath",
                                                                    required = true) String rdfNavLinkFilePath,
                                                            @RequestParam(value = "iscompilenode",
                                                                    required = false,
                                                                    defaultValue = "true") boolean isCompileNode,
                                                            @RequestParam(value = "iscompiletranseng",
                                                                    required = false,
                                                                    defaultValue = "false") boolean isCompileTransEng,
                                                            @RequestParam(value = "area",
                                                                    required = false,
                                                                    defaultValue = "") String area,
                                                            @RequestParam(value = "country",
                                                                    required = false,
                                                                    defaultValue = "") String country)
            throws SQLException, ParseException, InterruptedException, IOException, NoSuchFieldException {

        log.info("Starting optimized here link convert process for area: {}, country: {}", area, country);
        TimeInterval timer = DateUtil.timer();

        // Configure database context
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }

        // Get total record count for adaptive processing
        Integer totalRecords = streetsService.count();
        log.info("Total records to be processed: {}", totalRecords);

        // Calculate optimal step size if not provided
        int optimizedStep = calculateOptimalStepSize(step, totalRecords);
        log.info("Using optimized step size: {}", optimizedStep);

        // Initialize thread-safe collections
        Set<Integer> nodeIdSet = Collections.synchronizedSet(new TreeSet<>());

        // Calculate number of batches
        int totalBatches = (totalRecords + optimizedStep - 1) / optimizedStep;
        CountDownLatch countDownLatch = new CountDownLatch(totalBatches);

        // Load configuration files ONCE at the beginning (not in each batch)
        OptimizedXmlFileUtils optimizedXmlUtils = new OptimizedXmlFileUtils();
        Map<String, String> rdfCfMap = null;
        MultiValueMap<String, String> rdfLinkCfMap = null;
        Map<String, List<String>> rdfNavLinkMap = null;

        try {
            log.info("Loading configuration files once at startup...");
            rdfCfMap = optimizedXmlUtils.rdfCfFileRead(rdfCfFilePath);
            log.info("Loaded rdfCfMap with {} entries", rdfCfMap.size());

            rdfLinkCfMap = optimizedXmlUtils.rdfLinkCfFileRead(rdfCfLinkFilePath);
            log.info("Loaded rdfLinkCfMap with {} entries", rdfLinkCfMap.size());

            rdfNavLinkMap = optimizedXmlUtils.rdfNavLinkFileRead(rdfNavLinkFilePath, country);
            log.info("Loaded rdfNavLinkMap with {} entries", rdfNavLinkMap.size());

            // Process data in optimized batches with pre-loaded configuration
            processDataInOptimizedBatches(rdfCfMap, rdfLinkCfMap, rdfNavLinkMap,
                    isCompileNode, isCompileTransEng, area, country, nodeIdSet,
                    totalRecords, optimizedStep, countDownLatch);

            // Wait for all processing to complete
            countDownLatch.await();

        } catch (Exception e) {
            log.error("Error in optimized link convert process", e);
            throw e;
        } finally {
            // Explicit cleanup to help garbage collection
            if (rdfCfMap != null) {
                rdfCfMap.clear();
                rdfCfMap = null;
            }
            if (rdfLinkCfMap != null) {
                rdfLinkCfMap.clear();
                rdfLinkCfMap = null;
            }
            if (rdfNavLinkMap != null) {
                rdfNavLinkMap.clear();
                rdfNavLinkMap = null;
            }
            if (nodeIdSet != null) {
                nodeIdSet.clear();
                nodeIdSet = null;
            }

            // Suggest garbage collection (hint to JVM)
            System.gc();
        }

        log.info("Optimized here streets convert to link node cost time is {}s", timer.intervalSecond());
        return ResponseResult.OK(true, true);
    }

    /**
     * Calculate optimal step size based on data volume and system resources
     */
    private int calculateOptimalStepSize(int requestedStep, int totalRecords) {
        if (requestedStep > 0) {
            return requestedStep; // Use provided step if specified
        }

        // Adaptive step sizing based on data volume
        if (totalRecords <= 1000) {
            return Math.max(50, totalRecords / 10); // Small datasets: larger batches
        } else if (totalRecords <= 10000) {
            return 200; // Medium datasets: moderate batches
        } else if (totalRecords <= 100000) {
            return 500; // Large datasets: smaller batches for better memory management
        } else {
            return 1000; // Very large datasets: even smaller batches
        }
    }

    /**
     * Process data in optimized batches with proper memory management
     * Configuration data is pre-loaded and reused across all batches
     */
    private void processDataInOptimizedBatches(Map<String, String> rdfCfMap,
                                               MultiValueMap<String, String> rdfLinkCfMap,
                                               Map<String, List<String>> rdfNavLinkMap,
                                               boolean isCompileNode, boolean isCompileTransEng,
                                               String area, String country, Set<Integer> nodeIdSet,
                                               int totalRecords, int optimizedStep,
                                               CountDownLatch countDownLatch) {

        // Process in chunks to avoid loading all data into memory at once
        int totalBatches = (totalRecords + optimizedStep - 1) / optimizedStep;

        log.info("Processing {} total batches with pre-loaded configuration data", totalBatches);
        configureDatabaseContext(area, country, true);
        List<Mtdarea> allMtdareaCacheList = mtdareaService.lambdaQuery().list();
        Map<Long, Mtdarea> allMtdareaCacheMap = preloadMtdareaData();
        Map<Long, Mtddst> allMtddstCacheMap = preloadMtddstData();

        for (int batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
            final int currentBatch = batchIndex;
            final int offset = batchIndex * optimizedStep;

            log.info("Batch {}/{}: Processing with offset {} (reusing pre-loaded config data)",
                    currentBatch + 1, totalBatches, offset);

            try {
                // Configure database context for this batch
                if (!country.isEmpty()) {
                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
                }
                if (!area.isEmpty()) {
                    MybatisPlusConfig.myTableName.set("_" + area);
                } else {
                    MybatisPlusConfig.myTableName.set("");
                }

                // Fetch streets data for this batch
                List<Streets> streetsListi = streetsService.lambdaQuery()
                        .orderByDesc(Streets::getLinkId)
                        .last("limit " + optimizedStep + " offset " + offset)
                        .list();

                log.info("Batch {}/{}: Processing {} streets (offset: {})",
                        currentBatch + 1, totalBatches, streetsListi.size(), offset);

                if (!streetsListi.isEmpty()) {
                    // Use optimized service for better performance and memory management
                    // Note: Configuration data is reused from pre-loaded maps
                    optimizedLinkMService.optimizedLinkConvert(streetsListi, nodeIdSet, rdfLinkCfMap, rdfCfMap, rdfNavLinkMap, allMtdareaCacheMap, allMtddstCacheMap, allMtdareaCacheList,
                            isCompileNode, isCompileTransEng, area, country, countDownLatch);
                } else {
                    // No data in this batch, count down the latch
                    countDownLatch.countDown();
                }

            } catch (Exception e) {
                log.error("Error processing batch {}/{}", currentBatch + 1, totalBatches, e);
                countDownLatch.countDown(); // Ensure latch is decremented on error
            } finally {
                // Suggest garbage collection periodically (not after every batch)
                if (currentBatch % 10 == 0 && currentBatch > 0) { // Every 10 batches
                    log.info("Suggesting garbage collection after batch {}", currentBatch + 1);
                    System.gc();
                }
            }
        }
    }

    private Map<Long, Mtddst> preloadMtddstData() {
        // query all Mtddst data
        List<Mtddst> allMtddst = mtddstService.lambdaQuery().list();
        Map<Long, Mtddst> mtddstCache = allMtddst.stream()
                .collect(Collectors.toMap(Mtddst::getAreaId, mtddst -> mtddst, (existing, replacement) -> existing));

        log.info("Pre-loaded {} Mtddst entries for timezone/admin processing", mtddstCache.size());

        return mtddstCache;
    }

    private Map<Long, Mtdarea> preloadMtdareaData() {
        // query all Mtdarea data
        List<Mtdarea> allMtdarea = mtdareaService.lambdaQuery().list();

        // Create lookup map by area ID for fast access
        Map<Long, Mtdarea> mtdareaCache = allMtdarea.stream()
                .collect(Collectors.toMap(Mtdarea::getAreaId, mtdarea -> mtdarea, (existing, replacement) -> existing));

        log.info("Pre-loaded {} Mtdarea entries for timezone/admin processing", mtdareaCache.size());
        return mtdareaCache;
    }

    @ApiOperation(value = "here updatemainsubnode")
    @PostMapping("/updatemainsubnode")
    public ResponseResult<Boolean> hereUpdateMainSubNode(
            @RequestParam(value = "rdfcflinkfilepath",
                    required = true) String rdfCfLinkFilePath,
            @RequestParam(value = "rdfcffilepath",
                    required = true) String rdfCfFilePath,
            @RequestParam(value = "rdfcfnodefilepath",
                    required = true) String rdfCfNodeFilePath,
            @RequestParam(value = "area",
                    required = false,
                    defaultValue = "") String area,
            @RequestParam(value = "country",
                    required = false,
                    defaultValue = "") String country)
            throws ParseException, IOException {

//        Map<Integer,String> linkNodeIdMap=
//                streetsService.lambdaQuery().select(Streets::getLinkId,Streets::getRefInId,
//                        Streets::getNrefInId).list().stream().collect(Collectors.toMap(item->item.getLinkId(),
//                        item->item.getRefInId()+","+item.getNrefInId()));
//        log.info("linkNodeIdMap size is:"+linkNodeIdMap.size());
        // log.info("linkNodeIdMap is:"+linkNodeIdMap);

        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        final int areaAssign = StrUtil.isNotBlank(area)
                ? Integer.parseInt(area.replaceAll("\\D+", ""))
                : 0;

        TimeInterval timer = DateUtil.timer();
        // readRdfCf
        Map<String, String> rdfCfMap = new XmlFileUtils().rdfCfFileRead(rdfCfFilePath);
        log.info("read readRdfCf finished,size is:" + rdfCfMap.size());
        // readRdfCfLink
        MultiValueMap<String, String> rdfCfLinkMap = new XmlFileUtils().rdfCfLinkFileRead(rdfCfLinkFilePath);
        log.info("read rdfCfLink finished,size is:" + rdfCfLinkMap.size());
        // readRdfCfNode
        MultiValueMap<String, String> rdfCfNodeMap = new XmlFileUtils().rdfCfLinkFileRead(rdfCfNodeFilePath);
        log.info("read rdfCfNode finished,size is:" + rdfCfNodeMap.size());
        MultiValueMap<String, Integer> cfidNodesMap = new LinkedMultiValueMap<>();

        List<NodeM> nodeList = new ArrayList<>();

        for (String cfid : rdfCfMap.keySet()
        ) {
            List<String> linkIdlist = rdfCfLinkMap.get(cfid);
            List<String> nodeIdlist = rdfCfNodeMap.get(cfid);
            if (linkIdlist != null) {
                for (String linkId : linkIdlist
                ) {
                    List<Streets> streetsList = streetsService.lambdaQuery().select(Streets::getLinkId,
                            Streets::getRefInId, Streets::getNrefInId).eq(Streets::getLinkId, Integer.parseInt(linkId)).list();
                    for (Streets streets : streetsList
                    ) {
                        // cfidNodesMap.add(cfid,Integer.parseInt(linkNodeIdMap.get(cfid).split(",")[0]));
                        // cfidNodesMap.add(cfid,Integer.parseInt(linkNodeIdMap.get(cfid).split(",")[1]));
                        cfidNodesMap.add(cfid, streets.getRefInId().intValue());
                        cfidNodesMap.add(cfid, streets.getNrefInId().intValue());
                    }
                }
            }
            // 2
            if (nodeIdlist != null) {
                for (String nodeId : nodeIdlist
                ) {
                    NodeM node = new NodeM();
                    node.setNodeId(nodeId);
                    // 使用id继承，2022-01-20
                    List<Long> inheritID = inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(nodeId)));
                    node.setHllNodeid(inheritID.toString());
                    node.setType("2");
                    node.setUpDate(LocalDateTime.now());
                    node.setArea(areaAssign);
                    nodeList.add(node);
                }
            }
        }
        log.info("cfidNodesMap size is:" + cfidNodesMap.size());
        // 3,1
        for (String cfid : cfidNodesMap.keySet()
        ) {
            List<Integer> nodeIdList = cfidNodesMap.get(cfid).stream().distinct().collect(Collectors.toList());
            if (nodeIdList != null) {
                String subNodeid = "";
                String subNodeid2 = "";
                String subNodeString = "";

                for (int i = 1; i < nodeIdList.size(); i++) {
                    NodeM node = new NodeM();
                    node.setNodeId(nodeIdList.get(i).toString());
                    // 使用id继承，2022-01-20
                    // List<Long> inheritID = inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(nodeIdList.get(i).toString())));
                    // node.setHllNodeid(inheritID.get(0).toString());
                    node.setHllNodeid(node.getNodeId());
                    node.setType("1");
                    node.setMainnodeid(nodeIdList.get(0).toString());
                    // if (StrUtil.isNotEmpty(nodeIdList.get(0).toString())) {
                    //     node.setMainnodeid(String.valueOf(inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(nodeIdList.get(0).toString()))).get(0)));
                    // } else {
                    //     node.setMainnodeid(nodeIdList.get(0).toString());
                    //
                    // }
                    node.setUpDate(LocalDateTime.now());
                    node.setArea(areaAssign);
                    nodeList.add(node);
                    subNodeString += "|" + nodeIdList.get(i);
                }
                List<String> subNodeList = Arrays.asList(subNodeString.replaceFirst("\\|", "")
                        .split("\\|")).stream().distinct().collect(Collectors.toList());

                log.info("subNodeList size is:{},subNodeList is:{}", subNodeList.size(), subNodeList);
                subNodeString = String.join("|", subNodeList);

                NodeM node = new NodeM();
                if (subNodeString.length() > 256) {
                    String subNodeid1 = subNodeString.substring(0, 257);
                    subNodeid = subNodeid1.substring(0, subNodeid1.lastIndexOf("|"));
                    subNodeid2 = subNodeString.substring(subNodeid1.lastIndexOf("|") + 1);
                    // 处理id继承性
                    // if (StrUtil.isNotEmpty(subNodeid)) {
                    //     if (subNodeid.contains("|")) {
                    //         List<Long> inheritID = inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(subNodeid.split("\\|"))));
                    //         String resSubNodeid = CollUtil.join(inheritID, "|");
                    //         node.setSubnodeid(resSubNodeid);
                    //     } else {
                    //         node.setSubnodeid(String.valueOf(inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(subNodeid))).get(0)));
                    //     }
                    // } else {
                    //     node.setSubnodeid(subNodeid);
                    // }
                    node.setSubnodeid(subNodeid);
                    node.setSubnodeid2(subNodeid2);

                    // if (StrUtil.isNotEmpty(subNodeid2)) {
                    //     if (subNodeid2.contains("|")) {
                    //         List<Long> inheritID = inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(subNodeid2.split("\\|"))));
                    //         String resSubNodeid2 = CollUtil.join(inheritID, "|");
                    //         node.setSubnodeid2(resSubNodeid2);
                    //     } else {
                    //         node.setSubnodeid2(String.valueOf(inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(subNodeid2))).get(0)));
                    //     }
                    // } else {
                    //     node.setSubnodeid2(subNodeid2);
                    // }
                } else {
                    node.setSubnodeid(subNodeString);
                    // if (StrUtil.isNotEmpty(subNodeString)) {
                    //     if (subNodeString.contains("|")) {
                    //         List<Long> inheritID = inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(subNodeString.split("\\|"))));
                    //         String resSubNodeid = CollUtil.join(inheritID, "|");
                    //         node.setSubnodeid(resSubNodeid);
                    //     } else {
                    //         node.setSubnodeid(String.valueOf(inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(subNodeString))).get(0)));
                    //     }
                    // } else {
                    //     node.setSubnodeid(subNodeString);
                    // }
                }

                node.setNodeId(nodeIdList.get(0).toString());
                // 使用id继承，2022-01-20
                // List<Long> inheritID = inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(nodeIdList.get(0).toString())));
                // node.setHllNodeid(inheritID.get(0).toString());
                node.setHllNodeid(node.getNodeId());
                node.setType("3");
                // if (StrUtil.isNotEmpty(nodeIdList.get(0).toString())) {
                //     node.setMainnodeid(String.valueOf(inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(nodeIdList.get(0).toString()))).get(0)));
                // } else {
                //     node.setMainnodeid(nodeIdList.get(0).toString());
                // }
                node.setMainnodeid(nodeIdList.get(0).toString());
                node.setUpDate(LocalDateTime.now());
                node.setArea(areaAssign);
                log.info("subNodeid is:" + node.getSubnodeid());
                nodeList.add(node);
            }
        }
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        List<List<NodeM>> splitsNodeList = CollUtil.splitList(nodeList, 32767 / BeanUtil.beanToMap(new NodeM()).keySet().size());
        for (List<NodeM> nodes : splitsNodeList) {
            List<String> nodeIds = nodes.stream().map(NodeM::getNodeId).collect(Collectors.toList());
            List<Long> resNodeIds = inheritIDService.inheritID(new InheritIDDTO(12L, nodeIds));
            for (int i = 0; i < nodes.size(); i++) {
                nodes.get(i).setHllNodeid(String.valueOf(resNodeIds.get(i)));
            }

            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            nodeMService.updateBatchById(nodes);
        }
        log.info("update nodeList size is:" + nodeList.size());
        // if (!country.isEmpty()) {
        //     DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        // }
        // nodeService.updateBatchById(nodeList);
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        nodeMService.lambdaUpdate().isNull(NodeM::getType).set(NodeM::getType, "0")
                .set(NodeM::getUpDate, LocalDateTime.now()).update();
        log.info("update main sub node finished.cost time is {}s", timer.intervalSecond());
        log.info("update main sub node finished,country is {},area is {}", country, area);
        return ResponseResult.OK(true, true);
    }

    @ApiOperation(value = "here updatemainsubnode optimized - NEW OPTIMIZED VERSION")
    @PostMapping("/updatemainsubnode-optimized")
    public ResponseResult<Boolean> hereUpdateMainSubNodeOptimized(
            @RequestParam(value = "rdfcflinkfilepath",
                    required = true) String rdfCfLinkFilePath,
            @RequestParam(value = "rdfcffilepath",
                    required = true) String rdfCfFilePath,
            @RequestParam(value = "rdfcfnodefilepath",
                    required = true) String rdfCfNodeFilePath,
            @RequestParam(value = "area",
                    required = false,
                    defaultValue = "") String area,
            @RequestParam(value = "country",
                    required = false,
                    defaultValue = "") String country)
            throws ParseException, IOException {

        log.info("Starting optimized main sub node update for area: {}, country: {}", area, country);
        TimeInterval timer = DateUtil.timer();

        // Configure database context
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }

        // Load configuration files ONCE with optimized file utils
        OptimizedXmlFileUtils optimizedXmlUtils = new OptimizedXmlFileUtils();
        Map<String, String> rdfCfMap = null;
        MultiValueMap<String, String> rdfCfLinkMap = null;
        MultiValueMap<String, String> rdfCfNodeMap = null;

        try {
            log.info("Loading configuration files once at startup...");
            rdfCfMap = optimizedXmlUtils.rdfCfFileRead(rdfCfFilePath);
            log.info("Loaded rdfCfMap with {} entries", rdfCfMap.size());

            rdfCfLinkMap = optimizedXmlUtils.rdfLinkCfFileRead(rdfCfLinkFilePath);
            log.info("Loaded rdfCfLinkMap with {} entries", rdfCfLinkMap.size());

            rdfCfNodeMap = optimizedXmlUtils.rdfLinkCfFileRead(rdfCfNodeFilePath);
            log.info("Loaded rdfCfNodeMap with {} entries", rdfCfNodeMap.size());

            // Process node relationships with optimized memory management
            processNodeRelationshipsOptimized(rdfCfMap, rdfCfLinkMap, rdfCfNodeMap, area, country);

        } catch (Exception e) {
            log.error("Error in optimized main sub node update", e);
            throw e;
        } finally {
            // Explicit cleanup to help garbage collection
            if (rdfCfMap != null) {
                rdfCfMap.clear();
                rdfCfMap = null;
            }
            if (rdfCfLinkMap != null) {
                rdfCfLinkMap.clear();
                rdfCfLinkMap = null;
            }
            if (rdfCfNodeMap != null) {
                rdfCfNodeMap.clear();
                rdfCfNodeMap = null;
            }

            // Suggest garbage collection
            System.gc();
        }

        log.info("Optimized main sub node update finished. Cost time is {}s", timer.intervalSecond());
        log.info("Optimized main sub node update finished, country is {}, area is {}", country, area);
        return ResponseResult.OK(true, true);
    }

    /**
     * Process node relationships with optimized memory management
     */
    private void processNodeRelationshipsOptimized(Map<String, String> rdfCfMap,
                                                   MultiValueMap<String, String> rdfCfLinkMap,
                                                   MultiValueMap<String, String> rdfCfNodeMap,
                                                   String area, String country) {

        MultiValueMap<String, Integer> cfidNodesMap = new LinkedMultiValueMap<>();
        List<NodeM> nodeList = new ArrayList<>();

        log.info("Processing {} CF IDs for node relationships", rdfCfMap.size());

        // Process CF IDs in batches to manage memory
        int cfidBatchSize = 1000; // Process 1000 CF IDs at a time
        List<String> cfidList = new ArrayList<>(rdfCfMap.keySet());
        final int areaAssign = StrUtil.isNotBlank(area)
                ? Integer.parseInt(area.replaceAll("\\D+", ""))
                : 0;

        for (int batchStart = 0; batchStart < cfidList.size(); batchStart += cfidBatchSize) {
            int batchEnd = Math.min(batchStart + cfidBatchSize, cfidList.size());
            List<String> cfidBatch = cfidList.subList(batchStart, batchEnd);

            log.info("Processing CF ID batch {}-{} of {}", batchStart + 1, batchEnd, cfidList.size());

            // Process this batch of CF IDs
            processCfidBatch(cfidBatch, rdfCfMap, rdfCfLinkMap, rdfCfNodeMap, cfidNodesMap, nodeList, area, country);

            // Periodic garbage collection suggestion
            if (batchStart % (cfidBatchSize * 5) == 0 && batchStart > 0) {
                System.gc();
            }
        }

        log.info("cfidNodesMap size is: {}", cfidNodesMap.size());

        // Process main and sub nodes
        processMainAndSubNodes(cfidNodesMap, nodeList, areaAssign);

        // Area assignment is now handled within processMainAndSubNodes method

        // Save nodes in optimized batches
        saveNodesOptimized(nodeList, area, country);

        // Update nodes with null type
        updateNullTypeNodes(area, country);
    }

    /**
     * Process a batch of CF IDs to extract node relationships
     */
    private void processCfidBatch(List<String> cfidBatch, Map<String, String> rdfCfMap,
                                  MultiValueMap<String, String> rdfCfLinkMap,
                                  MultiValueMap<String, String> rdfCfNodeMap,
                                  MultiValueMap<String, Integer> cfidNodesMap,
                                  List<NodeM> nodeList, String area, String country) {

        for (String cfid : cfidBatch) {
            List<String> linkIdList = rdfCfLinkMap.get(cfid);
            List<String> nodeIdlist = rdfCfNodeMap.get(cfid);

            // FIX 1: Restore original Streets table lookup logic
            if (linkIdList != null) {
                for (String linkId : linkIdList) {
                    List<Streets> streetsList = streetsService.lambdaQuery().select(Streets::getLinkId,
                            Streets::getRefInId, Streets::getNrefInId).eq(Streets::getLinkId, Integer.parseInt(linkId)).list();
                    for (Streets streets : streetsList) {
                        cfidNodesMap.add(cfid, streets.getRefInId().intValue());
                        cfidNodesMap.add(cfid, streets.getNrefInId().intValue());
                    }
                }
            }

            // Process type "2" nodes
            if (nodeIdlist != null) {
                for (String nodeId : nodeIdlist) {
                    NodeM node = new NodeM();
                    node.setNodeId(nodeId);
                    List<Long> inheritID = inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(nodeId)));
                    node.setHllNodeid(inheritID.toString());
                    node.setType("2");
                    node.setUpDate(LocalDateTime.now());
                    // FIX 4: Add missing area assignment for type "2" nodes
                    final int areaAssign = StrUtil.isNotBlank(area)
                            ? Integer.parseInt(area.replaceAll("\\D+", ""))
                            : 0;
                    node.setArea(areaAssign);
                    nodeList.add(node);
                }
            }
        }
    }

    /**
     * Process main and sub nodes from CF ID mappings
     */
    private void processMainAndSubNodes(MultiValueMap<String, Integer> cfidNodesMap, List<NodeM> nodeList, int areaAssign) {
        for (String cfid : cfidNodesMap.keySet()) {
            List<Integer> nodeIdList = cfidNodesMap.get(cfid).stream().distinct().collect(Collectors.toList());

            if (nodeIdList != null && nodeIdList.size() > 1) {
                // FIX 2: Restore original complex sub-node string processing logic
                String subNodeid = "";
                String subNodeid2 = "";
                String subNodeString = "";

                // Process sub nodes (type "1") - maintaining original logic
                for (int i = 1; i < nodeIdList.size(); i++) {
                    NodeM node = new NodeM();
                    node.setNodeId(nodeIdList.get(i).toString());
                    node.setHllNodeid(node.getNodeId());
                    node.setType("1");
                    node.setMainnodeid(nodeIdList.get(0).toString());
                    node.setUpDate(LocalDateTime.now());
                    // FIX 5: Add missing area assignment for type "1" nodes
                    node.setArea(areaAssign);
                    nodeList.add(node);
                    subNodeString += "|" + nodeIdList.get(i);
                }

                // FIX 2 continued: Restore original string processing and length handling
                List<String> subNodeList = Arrays.asList(subNodeString.replaceFirst("\\|", "")
                        .split("\\|")).stream().distinct().collect(Collectors.toList());

                log.info("subNodeList size is:{},subNodeList is:{}", subNodeList.size(), subNodeList);
                subNodeString = String.join("|", subNodeList);

                // Process main node (type "3") with proper field assignments
                NodeM mainNode = new NodeM();
                if (subNodeString.length() > 256) {
                    String subNodeid1 = subNodeString.substring(0, 257);
                    subNodeid = subNodeid1.substring(0, subNodeid1.lastIndexOf("|"));
                    subNodeid2 = subNodeString.substring(subNodeid1.lastIndexOf("|") + 1);
                    mainNode.setSubnodeid(subNodeid);
                    mainNode.setSubnodeid2(subNodeid2);
                } else {
                    mainNode.setSubnodeid(subNodeString);
                }

                mainNode.setNodeId(nodeIdList.get(0).toString());
                mainNode.setHllNodeid(mainNode.getNodeId());
                mainNode.setType("3");
                // FIX 3: Add missing mainnodeid field assignment
                mainNode.setMainnodeid(nodeIdList.get(0).toString());
                mainNode.setUpDate(LocalDateTime.now());
                // FIX 6: Add missing area assignment for type "3" nodes
                mainNode.setArea(areaAssign);
                log.info("subNodeid is:" + mainNode.getSubnodeid());
                nodeList.add(mainNode);
            }
        }
    }

    /**
     * Save nodes in optimized batches with proper memory management
     */
    private void saveNodesOptimized(List<NodeM> nodeList, String area, String country) {
        if (nodeList.isEmpty()) {
            log.info("No nodes to save");
            return;
        }

        log.info("Saving {} nodes in optimized batches", nodeList.size());

        // Calculate optimal batch size based on field count
        int fieldNum = BeanUtil.beanToMap(new NodeM()).keySet().size();
        int batchSize = Math.min(32767 / fieldNum, 1000); // Cap at 1000 for memory management

        List<List<NodeM>> splitsNodeList = CollUtil.splitList(nodeList, batchSize);

        for (int batchIndex = 0; batchIndex < splitsNodeList.size(); batchIndex++) {
            List<NodeM> nodes = splitsNodeList.get(batchIndex);

            // Process ID inheritance in batches
            List<String> nodeIds = nodes.stream().map(NodeM::getNodeId).collect(Collectors.toList());
            List<Long> resNodeIds = inheritIDService.inheritID(new InheritIDDTO(12L, nodeIds));

            for (int i = 0; i < nodes.size(); i++) {
                nodes.get(i).setHllNodeid(String.valueOf(resNodeIds.get(i)));
            }

            // Set database context for this batch
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }

            // Save batch
            nodeMService.updateBatchById(nodes);

            log.info("Saved batch {}/{} with {} nodes", batchIndex + 1, splitsNodeList.size(), nodes.size());

            // Periodic cleanup
            if (batchIndex % 5 == 0 && batchIndex > 0) {
                System.gc();
            }
        }
    }

    /**
     * Update nodes with null type
     */
    private void updateNullTypeNodes(String area, String country) {
        // Set database context
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }

        // Update nodes with null type to "0"
        // FIX 7: Add missing setUpDate in the final update operation
        nodeMService.lambdaUpdate().isNull(NodeM::getType).set(NodeM::getType, "0")
                .set(NodeM::getUpDate, LocalDateTime.now()).update();
        log.info("Updated nodes with null type to '0'");
    }

    @ApiOperation(value = "here link updateformway")
    @PostMapping("/updateformway")
    public ResponseResult<Boolean> hereUpdateFormway(
            @RequestParam(value = "step",
                    required = false,
                    defaultValue = "1") int step,
            @RequestParam(value = "rdfcflinkfilepath",
                    required = true) String rdfCfLinkFilePath,
            @RequestParam(value = "rdfcffilepath",
                    required = true) String rdfCfFilePath,
            @RequestParam(value = "area",
                    required = false,
                    defaultValue = "") String area,
            @RequestParam(value = "country",
                    required = false,
                    defaultValue = "") String country)
            throws ParseException, IOException, SQLException, InterruptedException {

        // readRdfCf
        Map<String, String> rdfCfMap = new XmlFileUtils().rdfCfFileRead(rdfCfFilePath);
        log.info("read readRdfCf finished,size is:" + rdfCfMap.size());
        // readRdfCfLink
        MultiValueMap<String, String> rdfLinkCfMap = new XmlFileUtils().rdfLinkCfFileRead(rdfCfLinkFilePath);
        log.info("read rdfCfLink finished,size is:" + rdfLinkCfMap.size());
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }

        Integer listSize = streetsService.count();
        log.info("The records to be transfered:" + listSize);
        for (int i = 0; i <= listSize / step; i++) {
            List<Streets> streetsListi = streetsService.lambdaQuery()
                    .orderByDesc(Streets::getLinkId).last("limit " + step + " offset " + i * step).list();
            log.info("process start limit " + step + " offset " + i * step);
            if (streetsListi.size() > 0) {
                // linkSw2021q133Service.linkConvert(streetsListi,nodeIdSet,nodeList);
                linkMService.updateFormway(streetsListi, rdfLinkCfMap, rdfCfMap, area, country);
            }
        }

        return ResponseResult.OK(true, true);
    }

    @ApiOperation(value = "here link leftrule convert")
    @PostMapping("/leftruleconvert")
    public ResponseResult<Boolean> hereLinkLeftRuleConvert(@RequestParam(value = "step",
                                                                   required = false,
                                                                   defaultValue = "1") int step,
                                                           @RequestParam(value = "filepath") String filePath,
                                                           @RequestParam(value = "area",
                                                                   required = false,
                                                                   defaultValue = "") String area,
                                                           @RequestParam(value = "country",
                                                                   required = false,
                                                                   defaultValue = "") String country) throws IOException, ParseException {
        try {

            log.info("start left rule convert...");
            TimeInterval timer = DateUtil.timer();
            List<RuleM> ruleList = new XmlFileUtils().dividerFileRead(filePath);
            log.info("handle data cost time is {}s", timer.intervalSecond());
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            // filter ruleSw2021q133List data
            List<Streets> streets = streetsService.lambdaQuery().select(Streets::getLinkId).list();
            Set<Long> linkIds = streets.stream().map(Streets::getLinkId).collect(Collectors.toSet());
            ruleList = ruleList.stream().filter(s -> linkIds.contains(Long.parseLong(s.getInlinkId()))).collect(Collectors.toList());

            log.info("filter data cost time is {}s", timer.intervalSecond());
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            final int areaAssign = StrUtil.isNotBlank(area)
                    ? Integer.parseInt(area.replaceAll("\\D+", ""))
                    : 0;


            log.info("read rule finished,size is:" + ruleList.size());
            step = 32767 / BeanUtil.beanToMap(new RuleM()).keySet().size();
            List<List<RuleM>> partition = Lists.partition(ruleList, step);
            log.info("list to be processed:" + ruleList.size());
            for (int i = 0; i < partition.size(); i++) {
                try {
                    List<RuleM> rules = partition.get(i);
                    List<Long> ids = inheritIDService.createID(new InheritIDDTO(12L, Long.valueOf(rules.size())));

                    List<String> srcInlinkIds = rules.stream().map(RuleM::getInlinkId).collect(Collectors.toList());
                    List<String> srcOutlinkIds = rules.stream().map(RuleM::getOutlinkId).collect(Collectors.toList());
                    List<String> srcNodeIds = rules.stream().map(RuleM::getNodeId).collect(Collectors.toList());
                    List<Long> resInlinkIds = inheritIDService.inheritID(new InheritIDDTO(12L, srcInlinkIds));
                    List<Long> resOutlinkIds = inheritIDService.inheritID(new InheritIDDTO(12L, srcOutlinkIds));
                    List<Long> resNodeIds = inheritIDService.inheritID(new InheritIDDTO(12L, srcNodeIds));
                    for (int j = 0; j < rules.size(); j++) {
                        rules.get(j).setInlinkId(String.valueOf(resInlinkIds.get(j)));
                        rules.get(j).setOutlinkId(String.valueOf(resOutlinkIds.get(j)));
                        rules.get(j).setNodeId(String.valueOf(resNodeIds.get(j)));
                        rules.get(j).setRuleId(String.valueOf(ids.get(j)));
                        rules.get(j).setTileId(CommonUtils.getH3IndexByCentroid(nodeMService.getOne(Wrappers.<NodeM>lambdaQuery().eq(NodeM::getHllNodeid, rules.get(j).getNodeId())).getGeomwkt(), 7));
                        rules.get(j).setTileType(Const.H3);
                        rules.get(j).setArea(areaAssign);
                    }
                    ruleMapper.mysqlInsertOrUpdateBath(rules);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }


            log.info("left rule convert,cost time is {}s", timer.intervalSecond());
        } catch (Exception e) {
            e.printStackTrace();
            log.error("left rule convert error,{}", e.getMessage());
            return ResponseResult.OK(false, false);
        }
        return ResponseResult.OK(true, true);
    }

    @ApiOperation(value = "here link leftrule convert optimized - NEW OPTIMIZED VERSION")
    @PostMapping("/leftruleconvert-optimized")
    public ResponseResult<Boolean> hereLinkLeftRuleConvertOptimized(@RequestParam(value = "step",
                                                                            required = false,
                                                                            defaultValue = "0") int step,
                                                                    @RequestParam(value = "filepath") String filePath,
                                                                    @RequestParam(value = "area",
                                                                            required = false,
                                                                            defaultValue = "") String area,
                                                                    @RequestParam(value = "country",
                                                                            required = false,
                                                                            defaultValue = "") String country) throws IOException, ParseException {

        log.info("Starting optimized left rule convert process for area: {}, country: {}", area, country);
        TimeInterval timer = DateUtil.timer();

        // Load configuration files ONCE at the beginning using optimized file utils
        OptimizedXmlFileUtils optimizedXmlUtils = new OptimizedXmlFileUtils();
        List<RuleM> ruleList = null;
        Set<Long> linkIds = null;

        try {
            log.info("Loading rule data from file: {}", filePath);
            ruleList = loadRuleDataOptimized(filePath, optimizedXmlUtils);
            log.info("Loaded {} rules from file, cost time: {}s", ruleList.size(), timer.intervalSecond());

            // Configure database context for source data reading
            configureDatabaseContext(area, country, true);

            // Load link IDs for filtering with optimized query
            linkIds = loadLinkIdsOptimized();
            log.info("Loaded {} link IDs for filtering", linkIds.size());

            // Filter rules based on existing link IDs
            ruleList = filterRulesOptimized(ruleList, linkIds);
            log.info("Filtered rules, remaining: {}, cost time: {}s", ruleList.size(), timer.intervalSecond());

            // Configure database context for target data writing
            configureDatabaseContext(area, country, false);

            // Process rules in optimized batches with chunked querying
            processRulesInOptimizedBatches(ruleList, area, country);

            log.info("Optimized left rule convert completed, total cost time: {}s", timer.intervalSecond());

        } catch (Exception e) {
            log.error("Error in optimized left rule convert process", e);
            return ResponseResult.OK(false, false);
        } finally {
            // Explicit cleanup to help garbage collection
            cleanupOptimizedLeftRuleResources(ruleList, linkIds);
        }

        return ResponseResult.OK(true, true);
    }

    /**
     * Load rule data from file using optimized file utils with better resource management
     */
    private List<RuleM> loadRuleDataOptimized(String filePath, OptimizedXmlFileUtils optimizedXmlUtils) throws IOException {
        List<RuleM> ruleList = new ArrayList<>();

        optimizedXmlUtils.processFileInChunks(filePath, line -> {
            try {
                String[] splitResult = line.split("\t");
                if (splitResult.length >= 5) {
                    RuleM rule = new RuleM();
                    rule.setRuleId(UUID.randomUUID().toString());
                    rule.setInlinkId(splitResult[2]);
                    rule.setNodeId(splitResult[3]);
                    rule.setOutlinkId(splitResult[4]);
                    rule.setFlag(1);
                    rule.setUpDate(LocalDateTime.now());
                    rule.setDatasource("7");
                    rule.setStatus(0);
                    ruleList.add(rule);
                }
            } catch (Exception e) {
                log.warn("Error processing line: {}, error: {}", line, e.getMessage());
            }
        });

        return ruleList;
    }

    /**
     * Load link IDs for filtering with optimized query
     */
    private Set<Long> loadLinkIdsOptimized() {
        return streetsService.lambdaQuery()
                .select(Streets::getLinkId)
                .list()
                .stream()
                .map(Streets::getLinkId)
                .collect(Collectors.toSet());
    }

    /**
     * Filter rules based on existing link IDs with optimized stream processing
     */
    private List<RuleM> filterRulesOptimized(List<RuleM> ruleList, Set<Long> linkIds) {
        return ruleList.parallelStream()
                .filter(rule -> {
                    try {
                        return linkIds.contains(Long.parseLong(rule.getInlinkId()));
                    } catch (NumberFormatException e) {
                        log.warn("Invalid link ID format: {}", rule.getInlinkId());
                        return false;
                    }
                })
                .collect(Collectors.toList());
    }

    /**
     * Configure database context for source or target operations
     */
    private void configureDatabaseContext(String area, String country, boolean isSource) {
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, isSource));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
    }

    /**
     * Process rules in optimized batches with chunked querying to avoid PostgreSQL's 32767 parameter limit
     */
    private void processRulesInOptimizedBatches(List<RuleM> ruleList, String area, String country) {
        if (ruleList.isEmpty()) {
            log.info("No rules to process");
            return;
        }
        final int areaAssign = StrUtil.isNotBlank(area)
                ? Integer.parseInt(area.replaceAll("\\D+", ""))
                : 0;
        ruleList.forEach(rule -> rule.setArea(areaAssign));


        log.info("Processing {} rules in optimized batches", ruleList.size());

        // Calculate optimal batch size to avoid PostgreSQL parameter limit
        int maxFieldCount = BeanUtil.beanToMap(new RuleM()).keySet().size();
        int optimalBatchSize = Math.min(32767 / maxFieldCount, calculateAdaptiveBatchSize(ruleList.size()));

        log.info("Using optimized batch size: {} (max fields: {})", optimalBatchSize, maxFieldCount);

        // Split rules into batches using constant step size for sequential processing
        List<List<RuleM>> ruleBatches = Lists.partition(ruleList, optimalBatchSize);

        log.info("Processing {} batches with constant step size", ruleBatches.size());

        for (int batchIndex = 0; batchIndex < ruleBatches.size(); batchIndex++) {
            try {
                List<RuleM> batchRules = ruleBatches.get(batchIndex);
                log.info("Processing batch {}/{} with {} rules", batchIndex + 1, ruleBatches.size(), batchRules.size());

                // Process batch with optimized ID inheritance and tile information
                processBatchOptimized(batchRules, area, country, batchIndex + 1, ruleBatches.size());

                // Memory cleanup every few batches
                if (batchIndex % 5 == 0 && batchIndex > 0) {
                    log.info("Performing memory cleanup after batch {}", batchIndex + 1);
                    System.gc();
                }

            } catch (Exception e) {
                log.error("Error processing batch {}/{}", batchIndex + 1, ruleBatches.size(), e);
                // Continue with next batch instead of failing completely
            }
        }
    }

    /**
     * Calculate adaptive batch size based on data volume
     */
    private int calculateAdaptiveBatchSize(int totalRules) {
        if (totalRules <= 1000) {
            return Math.max(100, totalRules / 5); // Small datasets: larger batches
        } else if (totalRules <= 10000) {
            return 500; // Medium datasets: moderate batches
        } else if (totalRules <= 100000) {
            return 1000; // Large datasets: smaller batches for better memory management
        } else {
            return 1500; // Very large datasets: optimized batches
        }
    }

    /**
     * Process a single batch with optimized ID inheritance and tile information
     * Maintains 100% functional parity with original implementation
     */
    private void processBatchOptimized(List<RuleM> batchRules, String area, String country,
                                       int currentBatch, int totalBatches) {
        try {
            // Generate new IDs for rules using inheritIDService
            List<Long> ruleIds = inheritIDService.createID(new InheritIDDTO(12L, Long.valueOf(batchRules.size())));

            // Extract source IDs for inheritance
            List<String> srcInlinkIds = batchRules.stream().map(RuleM::getInlinkId).collect(Collectors.toList());
            List<String> srcOutlinkIds = batchRules.stream().map(RuleM::getOutlinkId).collect(Collectors.toList());
            List<String> srcNodeIds = batchRules.stream().map(RuleM::getNodeId).collect(Collectors.toList());

            // Inherit IDs for all link and node references
            List<Long> resInlinkIds = inheritIDService.inheritID(new InheritIDDTO(12L, srcInlinkIds));
            List<Long> resOutlinkIds = inheritIDService.inheritID(new InheritIDDTO(12L, srcOutlinkIds));
            List<Long> resNodeIds = inheritIDService.inheritID(new InheritIDDTO(12L, srcNodeIds));

            // Apply inherited IDs and set tile information for each rule
            for (int j = 0; j < batchRules.size(); j++) {
                RuleM rule = batchRules.get(j);

                // Set inherited IDs - maintaining exact field assignments from original
                rule.setInlinkId(String.valueOf(resInlinkIds.get(j)));
                rule.setOutlinkId(String.valueOf(resOutlinkIds.get(j)));
                rule.setNodeId(String.valueOf(resNodeIds.get(j)));
                rule.setRuleId(String.valueOf(ruleIds.get(j)));

                // Set tile information using node geometry - maintaining exact business logic
                setTileInformationOptimized(rule, area, country);
            }

            // Save batch to database using optimized batch insert
            int savedCount = ruleMapper.mysqlInsertOrUpdateBath(batchRules);
            log.info("Batch {}/{}: Saved {} rules successfully", currentBatch, totalBatches, savedCount);

        } catch (Exception e) {
            log.error("Error processing batch {}/{}", currentBatch, totalBatches, e);
            throw e;
        }
    }

    /**
     * Set tile information for rule using node geometry with optimized database access
     * Maintains 100% functional parity with original implementation
     */
    private void setTileInformationOptimized(RuleM rule, String area, String country) {
        try {
            // Get node geometry for tile calculation - exact same logic as original
            NodeM nodeM = nodeMService.getOne(Wrappers.<NodeM>lambdaQuery()
                    .eq(NodeM::getHllNodeid, rule.getNodeId()));

            if (nodeM != null && nodeM.getGeomwkt() != null) {
                // Calculate H3 tile ID using exact same method as original
                String tileId = CommonUtils.getH3IndexByCentroid(nodeM.getGeomwkt(), 7);
                rule.setTileId(tileId);
                rule.setTileType(Const.H3);
            }

        } catch (Exception e) {
            log.warn("Error setting tile information for rule: {}, error: {}", rule.getRuleId(), e.getMessage());
            // Continue processing even if tile information fails
        }
    }

    /**
     * Cleanup resources for optimized left rule convert with proper memory management
     */
    private void cleanupOptimizedLeftRuleResources(List<RuleM> ruleList, Set<Long> linkIds) {
        try {
            // Clear collections to help garbage collection
            if (ruleList != null) {
                ruleList.clear();
            }
            if (linkIds != null) {
                linkIds.clear();
            }

            // Clear thread-local variables
            DynamicDataSourceContextHolder.clear();
            MybatisPlusConfig.myTableName.remove();

            // Suggest garbage collection to help with memory management
            System.gc();

            log.debug("Optimized left rule resources cleaned up and garbage collection suggested");

        } catch (Exception e) {
            log.warn("Error during resource cleanup: {}", e.getMessage());
        }
    }

    @GetMapping("updateVehclType")
    public ResponseResult<String> updateVehclType(@RequestParam(value = "area", required = false, defaultValue = "") String area,
                                                  @RequestParam(value = "country", required = false, defaultValue = "") String country) {

        Integer updateNum = 0;
        try {
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            // 1. 获取交规中vehclType为null的数据
            List<RuleM> ruleList = ruleService.lambdaQuery().isNull(RuleM::getVehclType).list();
            log.info("交规车辆类型为空的存在,ruleList size is {}", ruleList.size());
            // List<String> linkIds = ruleList.stream().map(RuleM::getInlinkId).collect(Collectors.toList());
            // 2.查询对应的vehclType,分批处理linkIds
            List<List<RuleM>> batchRules = CollUtil.splitList(ruleList, 32767 / BeanUtil.beanToMap(new RuleM()).keySet().size());
            for (List<RuleM> batchRule : batchRules) {
                List<String> batchRuleIds = batchRule.stream().map(RuleM::getInlinkId).collect(Collectors.toList());
                List<LinkM> linkMList = linkMService.lambdaQuery().select(LinkM::getArVeh, LinkM::getHllLinkid).in(LinkM::getHllLinkid, batchRuleIds).list();

                for (RuleM rule : batchRule) {
                    for (LinkM linkM : linkMList) {
                        if (rule.getInlinkId().equals(linkM.getHllLinkid())) {
                            rule.setVehclType(linkM.getArVeh());
                        }
                    }
                }
                // 3.更新rule表中的vehclType
                updateNum += ruleMapper.mysqlInsertOrUpdateBath(batchRule);
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new RuntimeException(e);
        }
        return ResponseResult.OK("更新交规车辆类型成功，更新数量:" + updateNum, true);
    }

    @GetMapping("updatePubAccess")
    public ResponseResult<String> updatePubAccess(@RequestParam(value = "area", required = false, defaultValue = "") String area,
                                                  @RequestParam(value = "country", required = false, defaultValue = "") String country) {

        Integer updateNum = 0;
        try {
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            // 1. 获取streets linkid,pubAccess的数据
            List<Streets> streetsList = streetsService.lambdaQuery().select(Streets::getLinkId, Streets::getPubAccess, Streets::getArTraff).list();
            log.info("需要升级的pubAccess size is {}", streetsList.size());
            // 2.分批处理linkIds
            List<List<Streets>> batchStreets = CollUtil.splitList(streetsList, 32767 / BeanUtil.beanToMap(new LinkM()).keySet().size());
            for (List<Streets> batchStreet : batchStreets) {
                log.info("process sublist size is {}", batchStreet.size());
                List<Long> linkIds = batchStreet.stream().map(Streets::getLinkId).collect(Collectors.toList());
//                Map<Long,String> linkIdMap = batchStreet.stream().collect(Collectors.toMap(Streets::getLinkId,Streets::getPubAccess));
                if (!country.isEmpty()) {
                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                }
                if (!area.isEmpty()) {
                    MybatisPlusConfig.myTableName.set("_" + area);
                } else {
                    MybatisPlusConfig.myTableName.set("");
                }
                List<LinkM> linkMList = linkMService.lambdaQuery().in(LinkM::getLinkId, linkIds.stream().map(String::valueOf).collect(Collectors.toList())).list();
//                List<LinkM> linkMList = linkMService.lambdaQuery().in(LinkM::getLinkId, linkIdMap.keySet().stream().map(String::valueOf).collect(Collectors.toList())).list();
                for (Streets streets : batchStreet) {
                    for (LinkM linkM : linkMList) {
                        if (streets.getLinkId().equals(Long.parseLong(linkM.getLinkId()))) {
                            linkM.setPubAccess(streets.getPubAccess());
                            if ("N".equals(streets.getPubAccess())) {
                                if ("1".equals(linkM.getFormway())) {
                                    linkM.setFormway("52");
                                } else {
                                    log.info("linkId is {},formway is {}", linkM.getLinkId(), linkM.getFormway());
                                    linkM.setFormway(linkM.getFormway() + ",52");
                                }
                            }
                            if ("N".equals(streets.getArTraff())) {
                                if ("1".equals(linkM.getFormway())) {
                                    linkM.setFormway("55");
                                } else {
                                    linkM.setFormway(linkM.getFormway() + ",55");
                                }
                            }
                        }
                    }
                }
//                batchStreet.forEach(streets -> linkMList.stream()
//                        .filter(linkM -> streets.getLinkId().toString().equals(linkM.getLinkId()))
//                        .findFirst()
//                        .ifPresent(linkM -> linkM.setPubAccess(streets.getPubAccess())));

//                for (LinkM linkM : linkMList) {
//                    if (linkIdMap.containsKey(Long.parseLong(linkM.getLinkId()))) {
//                        linkM.setPubAccess(linkIdMap.get(Long.parseLong(linkM.getLinkId())));
//                        if("N".equals(linkIdMap.get(Long.parseLong(linkM.getLinkId())))){
//                            linkM.setFormway(linkM.getFormway()+",52");
//                        }
//                    }
//                }
                // 3.更新linkM表中的pubAccess
                updateNum += linkMMapper.mysqlInsertOrUpdateBath(linkMList);
                // log.info("update linkM pubAccess success,update num is {}", updateNum);
            }
            log.info("update linkM pubAccess success,update num is {}", updateNum);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new RuntimeException(e);
        }
        return ResponseResult.OK("更新LinkM->pubAccess成功，更新数量:" + updateNum, true);
    }

    @ApiOperation(value = "update namelangcd")
    @PostMapping("/updatenamelangcd")
    public ResponseResult<Boolean> updateNameLangCd(
            @RequestParam(value = "step",
                    required = false,
                    defaultValue = "1") int step,
            @RequestParam(value = "area", required = false, defaultValue = "") String area,
            @RequestParam(value = "country", required = false, defaultValue = "") String country) throws InterruptedException {
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        TimeInterval timer = DateUtil.timer();
        log.info("update namelangcd start");
        Integer listSize = streetsService.count();
        CountDownLatch countDownLatch = new CountDownLatch(listSize / step + 1);
        log.info("The records to be transfered:" + listSize);
        for (int i = 0; i <= listSize / step; i++) {
//            if (i == 0) {
//                step = 100;
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            List<Streets> streetsListi = streetsService.lambdaQuery()
                    .orderByDesc(Streets::getLinkId).last("limit " + step + " offset " + i * step).list();
            log.info("process start limit " + step + " offset " + i * step);
            if (streetsListi.size() > 0) {
                // linkSw2021q133Service.linkConvert(streetsListi,nodeIdSet,nodeList);
                linkMService.updateNameLangcd(streetsListi, area, country, countDownLatch);
            }
//            }
        }
        countDownLatch.await();
        log.info("update namelangcd cost time is {}s", timer.intervalSecond());
        log.info("update namelangcd end");
        return ResponseResult.OK(true, true);
    }

    @ApiOperation(value = "get link by link id")
    @GetMapping("/getbylinkid")
    // used for oversea evaluate
    public ResponseResult<Link> getLinkById(@RequestParam(value = "linkid") String linkId,
                                            @RequestParam(value = "market", required = false, defaultValue = "phl") String market,
                                            @RequestParam(value = "dataVersion", required = false, defaultValue = "2023_q1") String dataVersion) {
        DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(market, dataVersion, false));
        MybatisPlusConfig.myTableName.set("");
        log.info("linkMapper threadlocal info is:" + Thread.currentThread().getName());
        log.info("link ds is:" + DynamicDataSourceContextHolder.peek());
        log.info("request linkid is:" + linkId);
        log.info("request market is:" + market);
        log.info("request dataVersion is:" + dataVersion);
        if (linkId.contains(".")) {
            linkId = linkId.split("\\.")[1];
            log.info("linkid is:" + linkId);
        }
        //handle version different cause start from version:2023_q2 added divider and divider_leg columns
        if ("2023_q1".equals(dataVersion)) {
            return ResponseResult.OK(linkService.lambdaQuery().eq(Link::getHllLinkid, linkId)
                    .select(Link.class, i -> !i.getColumn().equals("divider") && !i.getColumn().equals("divider_leg")).list().get(0), true);
        } else {
            Link link = linkService.lambdaQuery()
                    .eq(Link::getHllLinkid, linkId)
                    .select(Link.class, i -> !i.getColumn().equals("pub_access"))
                    .list()
                    .get(0);
            // 将 WKB 转换为 WKT
            if (link.getGeometry() != null && link.getGeomwkt() == null) {
                WKBReader reader = new WKBReader();
                WKTWriter writer = new WKTWriter();
                try {
                    link.setGeomwkt(writer.write(reader.read(hexToBytes(link.getGeometry()))));
                } catch (Exception e) {
                    log.error("Failed to convert WKB to WKT", e);
                }
            }
            return ResponseResult.OK(link, true);
        }
    }

    @ApiOperation(value = "get node by node id")
    @GetMapping("/getbynodeid")
    // used for oversea evaluate
    public ResponseResult<Node> getNodeById(@RequestParam(value = "nodeid") String nodeId,
                                            @RequestParam(value = "market", required = false, defaultValue = "phl") String market,
                                            @RequestParam(value = "dataVersion", required = false, defaultValue = "2023_q1") String dataVersion) {
        DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(market, dataVersion, false));
        MybatisPlusConfig.myTableName.set("");
        log.info("nodeMapper threadlocal info is:" + Thread.currentThread().getName());
        log.info("node ds is:" + DynamicDataSourceContextHolder.peek());
        log.info("request nodeid is:" + nodeId);
        log.info("request area is:" + market);
        log.info("request dataVersion is:" + dataVersion);
        if (nodeId.contains(".")) {
            nodeId = nodeId.split("\\.")[1];
            log.info("nodeid is:" + nodeId);
        }
        Node node = nodeService.lambdaQuery().eq(Node::getHllNodeid, nodeId).list().get(0);
        if (node.getGeometry() != null && node.getGeomwkt() == null) {
            WKBReader reader = new WKBReader();
            WKTWriter writer = new WKTWriter();
            try {
                node.setGeomwkt(writer.write(reader.read(hexToBytes(node.getGeometry()))));
            } catch (Exception e) {
                log.error("Failed to convert WKB to WKT", e);
            }
        }
        return ResponseResult.OK(node, true);
    }

    @ApiOperation(value = "get rule by link id")
    @GetMapping("/getrulebylinkid")
    // used for oversea evaluate
    public ResponseResult<List<RuleSw2021q133>> getRuleLinkById(@RequestParam(value = "linkid") String linkId,
                                                                @RequestParam(value = "market", required = false, defaultValue = "phl") String market,
                                                                @RequestParam(value = "dataVersion", required = false, defaultValue = "2023_q1") String dataVersion) {
        DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(market, dataVersion, false));
        MybatisPlusConfig.myTableName.set("");
        log.info("ruleMapper threadlocal info is:" + Thread.currentThread().getName());
        log.info("rule ds is:" + DynamicDataSourceContextHolder.peek());
        log.info("request linkid is:" + linkId);
        log.info("request area is:" + market);
        log.info("request dataVersion is:" + dataVersion);
        if (linkId.contains(".")) {
            linkId = linkId.split("\\.")[1];
            log.info("linkid is:" + linkId);
        }
        return ResponseResult.OK(ruleSw2021q133Service.lambdaQuery().eq(RuleSw2021q133::getInlinkId, linkId).list(), true);
    }

    @ApiOperation(value = "get relation by link id")
    @GetMapping("/getrelationbylinkid")
    // used for oversea evaluate
    public ResponseResult<List<RelationSw2021q133>> getRelationByLinkId(@RequestParam(value = "linkid") String linkId,
                                                                        @RequestParam(value = "market", required = false, defaultValue = "phl") String market,
                                                                        @RequestParam(value = "dataVersion", required = false, defaultValue = "2023_q1") String dataVersion) {
        DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(market, dataVersion, false));
        MybatisPlusConfig.myTableName.set("");
        log.info("ruleMapper threadlocal info is:" + Thread.currentThread().getName());
        log.info("rule ds is:" + DynamicDataSourceContextHolder.peek());
        log.info("request linkid is:" + linkId);
        log.info("request area is:" + market);
        log.info("request dataVersion is:" + dataVersion);
        if (linkId.contains(".")) {
            linkId = linkId.split("\\.")[1];
            log.info("linkid is:" + linkId);
        }
        return ResponseResult.OK(relationSw2021q133ServiceImpl.lambdaQuery()
                .eq(RelationSw2021q133::getInlinkId, linkId)
                .or()
                .eq(RelationSw2021q133::getOutlinkId, linkId)
                .list(), true);
    }

    @ApiOperation(value = "get rule and relation by link id")
    @GetMapping("/getRuleRelationByLinkId")
    // used for oversea evaluate
    public ResponseResult<Map<String, Object>> getRuleRelationByLinkId(@RequestParam(value = "linkid") String linkId,
                                                                       @RequestParam(value = "market", required = false, defaultValue = "phl") String market,
                                                                       @RequestParam(value = "dataVersion", required = false, defaultValue = "2023_q1") String dataVersion) {
        DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(market, dataVersion, false));
        MybatisPlusConfig.myTableName.set("");
        log.info("ruleMapper threadlocal info is:" + Thread.currentThread().getName());
        log.info("rule ds is:" + DynamicDataSourceContextHolder.peek());
        log.info("request linkid is:" + linkId);
        log.info("request area is:" + market);
        log.info("request dataVersion is:" + dataVersion);
        if (linkId.contains(".")) {
            linkId = linkId.split("\\.")[1];
            log.info("linkid is:" + linkId);
        }
        List<RuleSw2021q133> ruleSw2021q133List = ruleSw2021q133Service.lambdaQuery().eq(RuleSw2021q133::getInlinkId, linkId).list();
        List<RelationSw2021q133> relationSw2021q133List = relationSw2021q133ServiceImpl.lambdaQuery()
                .eq(RelationSw2021q133::getInlinkId, linkId)
                .or()
                .eq(RelationSw2021q133::getOutlinkId, linkId)
                .list();

        Map<String, Object> result = new HashMap<>();
        result.put("rules", ruleSw2021q133List);
        result.put("relations", relationSw2021q133List);
        return ResponseResult.OK(result, true);
//        return ResponseResult.OK(ruleSw2021q133Service.lambdaQuery().eq(RuleSw2021q133::getInlinkId, linkId).list(), true);
    }

    @ApiOperation(value = "here link update ar_veh")
    @PostMapping("/updatearveh")
    public ResponseResult<Boolean> hereUpdateArVeh(@RequestParam(value = "step",
                                                           required = false,
                                                           defaultValue = "1") int step,
                                                   @RequestParam(value = "area",
                                                           required = false,
                                                           defaultValue = "") String area,
                                                   @RequestParam(value = "country",
                                                           required = false,
                                                           defaultValue = "") String country)
            throws InterruptedException {

        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }

        Integer listSize = streetsService.count();
        CountDownLatch countDownLatch = new CountDownLatch(listSize / step + 1);
        log.info("The records to be update:" + listSize);
        for (int i = 0; i <= listSize / step; i++) {
//            if (i == 0) {
//                step = 100;
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            List<Streets> streetsListi = streetsService.lambdaQuery()
                    .orderByDesc(Streets::getLinkId).last("limit " + step + " offset " + i * step).list();
            log.info("process start limit " + step + " offset " + i * step);
            if (streetsListi.size() > 0) {
                linkMService.upateArVeh(streetsListi, area, country, countDownLatch);
            }
//            }
        }
        countDownLatch.await();
        return ResponseResult.OK(true, true);
    }

    @ApiOperation(value = "here link update timeZone")
    @PostMapping("/updateTimeZone")
    public ResponseResult<Boolean> hereUpdateTimeZone(@RequestParam(value = "step",
                                                              required = false,
                                                              defaultValue = "1") int step,
                                                      @RequestParam(value = "area",
                                                              required = false,
                                                              defaultValue = "") String area,
                                                      @RequestParam(value = "country",
                                                              required = false,
                                                              defaultValue = "") String country)
            throws InterruptedException {

        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }

        Integer listSize = streetsService.count();
        CountDownLatch countDownLatch = new CountDownLatch(listSize / step + 1);
        log.info("The records to be update:" + listSize);
        for (int i = 0; i <= listSize / step; i++) {
            // if (i == 0) {
            //     step = 10;
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            List<Streets> streetsListi = streetsService.lambdaQuery()
                    .orderByDesc(Streets::getLinkId).last("limit " + step + " offset " + i * step).list();
            log.info("process start limit " + step + " offset " + i * step);
            if (streetsListi.size() > 0) {
                linkMService.upateTimeZone(streetsListi, area, country, countDownLatch);
            }
        }
        // }
        countDownLatch.await();
        return ResponseResult.OK(true, true);
    }


    public static void main(String[] args) {

        String[] subnode1 = {"888379025", "968214451", "888379024", "888379025", "968214451", "888379024"};
        String subNodeString = "";
        for (int i = 0; i < subnode1.length; i++) {
            subNodeString += "|" + subnode1[i];
        }

        List<String> subNodeList = Arrays.asList(subNodeString.replaceFirst("\\|", "")
                .split("\\|")).stream().distinct().collect(Collectors.toList());

        String nodeString = "888379025,968214451,888379024,970184653,970184651,884391803,888379025,888379026,970184653,970201474,968214450,970184652,968214449,970184653,884391803,970184652,970184653,970201474,884391803,888379026,968214448,888379026,968214449,968214448,970184651,884391803,888379024,968214450,888379024";
//        List<String> subNodeList = Arrays.asList(nodeString.replaceFirst(",","")
//                .split(",")).stream().distinct().collect(Collectors.toList());
//        nodeString = String.join(",",subNodeList);
        if (nodeString.length() > 256) {
            String subNodeid1 = nodeString.substring(0, 257);
            String subNodeid = subNodeid1.substring(0, subNodeid1.lastIndexOf(","));
            System.out.println(subNodeid);
            System.out.println("subNodeId length is:" + subNodeid.length());
            String subNodeid2 = nodeString.substring(subNodeid1.lastIndexOf(",") + 1);
            System.out.println(subNodeid2);
            System.out.println("subNodeId2 length is:" + subNodeid2.length());
        }
    }

    @ApiOperation("map link diff column to link_rp")
    @GetMapping("convert2rp")
    public ResponseResult<Boolean> convert2rp(@RequestParam(value = "step", required = false, defaultValue = "1") int step,
                                              @RequestParam(value = "area", required = false, defaultValue = "") String area,
                                              @RequestParam(value = "country", required = false, defaultValue = "") String country) throws InterruptedException {
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        TimeInterval timer = DateUtil.timer();
        // calculate loop times
        int linkCount = linkMService.count();
        int loop = linkCount % step != 0 ? (linkCount / step) + 1 : linkCount / step;
        CountDownLatch countDownLatch = new CountDownLatch(loop);
        for (int i = 0; i < loop; i++) {
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            log.info("query link db is:" + DynamicDataSourceContextHolder.peek());
            List<LinkM> linkList = linkMService.lambdaQuery().ne(LinkM::getStatus, 1)
                    .orderByDesc(LinkM::getLinkId).last("limit " + step + " offset " + i * step).list();
            log.info("convert link db is:" + DynamicDataSourceContextHolder.peek());
            linkService.convert2rp(area, country, countDownLatch, linkList);
        }
        countDownLatch.await();
        log.info("map link diff column to link_rp cost time is {}s", timer.intervalSecond());
        return ResponseResult.OK(true, true);
    }

    @ApiOperation("map link diff column to link_rp - OPTIMIZED VERSION")
    @GetMapping("convert2rp-optimized")
    public ResponseResult<Boolean> convert2rpOptimized(@RequestParam(value = "step", required = false, defaultValue = "0") int step,
                                                       @RequestParam(value = "area", required = false, defaultValue = "") String area,
                                                       @RequestParam(value = "country", required = false, defaultValue = "") String country) throws InterruptedException {

        TimeInterval timer = DateUtil.timer();
        log.info("Starting optimized convert2rp process for area: {}, country: {}", area, country);

        try {
            // Configure database context for source data reading
            configureDatabaseContext(area, country, true);

            // Get total record count for processing
            int totalRecords = linkMService.count();
            log.info("Total LinkM records to process: {}", totalRecords);

            if (totalRecords == 0) {
                log.info("No records to process");
                return ResponseResult.OK(true, true);
            }

            // Calculate optimized step size based on data volume and available memory
            int optimizedStep = calculateOptimizedStepSize(step, totalRecords);
            log.info("Using optimized step size: {} (original: {})", optimizedStep, step);

            // Process data in optimized batches
            processLinkDataInOptimizedBatches(area, country, totalRecords, optimizedStep);

            log.info("Optimized convert2rp completed, total cost time: {}s", timer.intervalSecond());

        } catch (Exception e) {
            log.error("Error in optimized convert2rp process", e);
            return ResponseResult.OK(false, false);
        } finally {
            // Explicit cleanup to help garbage collection
            cleanupOptimizedConvert2rpResources();
        }

        return ResponseResult.OK(true, true);
    }

    /**
     * Process LinkM data in optimized batches with proper memory management
     */
    private void processLinkDataInOptimizedBatches(String area, String country, int totalRecords, int optimizedStep) throws InterruptedException {

        // Process in chunks to avoid loading all data into memory at once
        int totalBatches = (totalRecords + optimizedStep - 1) / optimizedStep;

        log.info("Processing {} total batches for convert2rp", totalBatches);

        // Use optimized thread pool configuration
        CountDownLatch countDownLatch = new CountDownLatch(totalBatches);

        for (int i = 0; i < totalBatches; i++) {
            int offset = i * optimizedStep;

            log.info("Processing batch {}/{} with offset: {}, step: {}", i + 1, totalBatches, offset, optimizedStep);

            // Configure database context for each batch to ensure consistency
            configureDatabaseContext(area, country, true);

            // Query data for this batch
            List<LinkM> linkList = linkMService.lambdaQuery()
                    .ne(LinkM::getStatus, 1)
                    .orderByDesc(LinkM::getLinkId)
                    .last("limit " + optimizedStep + " offset " + offset)
                    .list();

            if (!linkList.isEmpty()) {
                // Use optimized service for better performance and memory management
                linkService.convert2rpOptimized(area, country, countDownLatch, linkList);
            } else {
                // No data in this batch, count down the latch
                countDownLatch.countDown();
            }

            // Cleanup after every few batches to manage memory
            if (i % 3 == 0) {
                cleanupOptimizedConvert2rpResources();
                logMemoryUsage("After batch " + (i + 1));
            }
        }

        countDownLatch.await();
        log.info("All convert2rp batches completed successfully");
    }

    /**
     * Calculate optimized step size based on data volume and available memory
     */
    private int calculateOptimizedStepSize(int originalStep, int totalRecords) {
        if (originalStep > 0) {
            return originalStep; // Use provided step if specified
        }

        // Calculate based on available memory and record count
        Runtime runtime = Runtime.getRuntime();
        long availableMemoryMB = (runtime.maxMemory() - runtime.totalMemory() + runtime.freeMemory()) / (1024 * 1024);

        int optimizedStep;
        if (totalRecords > 100000) {
            // Large dataset: use smaller batches
            optimizedStep = Math.max(1000, Math.min(5000, (int) (availableMemoryMB / 10)));
        } else if (totalRecords > 10000) {
            // Medium dataset: moderate batches
            optimizedStep = Math.max(500, Math.min(2000, (int) (availableMemoryMB / 5)));
        } else {
            // Small dataset: larger batches
            optimizedStep = Math.max(100, Math.min(1000, totalRecords / 2));
        }

        log.info("Calculated optimized step size: {} for {} records with {}MB available memory",
                optimizedStep, totalRecords, availableMemoryMB);

        return optimizedStep;
    }

    /**
     * Cleanup resources to help garbage collection
     */
    private void cleanupOptimizedConvert2rpResources() {
        try {
            System.gc(); // Suggest garbage collection
            Thread.sleep(10); // Brief pause to allow cleanup
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * Log current memory usage for monitoring
     */
    private void logMemoryUsage(String context) {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory() / (1024 * 1024);
        long freeMemory = runtime.freeMemory() / (1024 * 1024);
        long usedMemory = totalMemory - freeMemory;
        long maxMemory = runtime.maxMemory() / (1024 * 1024);

        log.info("{} - Memory usage: Used: {}MB, Free: {}MB, Total: {}MB, Max: {}MB",
                context, usedMemory, freeMemory, totalMemory, maxMemory);
    }


    @ApiOperation(value = "link diff and merge")
    @PostMapping("/diffMerge")
    public ResponseResult<Boolean> diffMerge() {

        // 1.osm数据入库

        // 2.获取osm数据
        List<Road> roadList = roadMapper.selectList(null);
        List<Road> insertRoadList = new ArrayList<>();
        // 3.osm数据空间查询link中的数据
        for (Road road : roadList) {
            String lastSql = "";
            List<LinkM> linkList = linkMService.lambdaQuery().last(lastSql).list();
            if (CollUtil.isNotEmpty(linkList)) {
                // 3.1 获取空间查询数据中，离osm最近的数据
                BiMap<String, Double> tmpMap = new BiMap<>(new HashMap<>());
                for (LinkM link : linkList) {
                    // double distance = calculationMapper.calLineDistance(road.getGeom(), link.getGeomwkt());
                    // tmpMap.put(link.getLinkId(), distance);
                }
                List<Double> values = (List<Double>) tmpMap.values();
                Double minDistance = values.stream().min(Double::compareTo).get();
                String linkId = tmpMap.getKey(minDistance);
                LinkM link = linkList.stream().filter(s -> linkId.equals(s.getLinkId())).collect(Collectors.toList()).get(0);
                // 3.2 计算相似度，判断匹配的here道路是否需要差分

            } else {
                insertRoadList.add(road);
            }
        }
        // TODO

        // 4.查询出的link数据，按与osm道路距离排序，选取最小的

        // 5.通过坐标，算出相似度

        return ResponseResult.OK(true, true);
    }

    @ApiOperation(value = "link road match")
    @PostMapping("/roadMatch")
    public ResponseResult<Boolean> roadMatch(@RequestParam(required = false, defaultValue = "") String country,
                                             @RequestParam(required = false, defaultValue = "") String area,
                                             @RequestParam int step,
                                             @RequestParam int version) throws InterruptedException {

        TimeInterval timer = DateUtil.timer();
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        String searchSql = "limit " + step + " offset 0";
        // List<Road> roadList = roadService.lambdaQuery().last(searchSql).list();
        // List<Road> roadList = roadService.lambdaQuery().eq(Road::getOsmId,"148103098").list();

        // 1.根据版本号筛选osm截断后的数据
        // List<RoadBreak> roadList = roadBreakService.lambdaQuery().eq(RoadBreak::getVersion, version).list();
        // List<RoadBreak> roadList = roadBreakService.lambdaQuery().eq(RoadBreak::getOsmId,"929532963").list();

        // 2.多线程处理参数准备
        int count = roadBreakService.lambdaQuery().eq(RoadBreak::getVersion, version).count();
        int fieldNum = BeanUtil.beanToMap(new RoadMatchRes()).keySet().size();
        int batchSize = 32767 / fieldNum;
        log.info("batchInsert size is {}", batchSize);
        CountDownLatch countDownLatch = new CountDownLatch(count / batchSize + 1);

        for (int i = 0; i < count / batchSize + 1; i++) {
            List<RoadBreak> batchList = roadBreakService.lambdaQuery().eq(RoadBreak::getVersion, version).orderByDesc(RoadBreak::getRoadId).last("limit " + batchSize + " offset " + i * batchSize).list();
            roadMatchResService.roadMatch(batchList, country, area, countDownLatch);
        }

        countDownLatch.await();

        log.info("匹配道路数据入库，耗时{}s", timer.intervalSecond());
        return ResponseResult.OK(true, true);

    }

    @ApiOperation(value = "osm road break")
    @PostMapping("/roadBreak")
    public ResponseResult<Boolean> roadBreak(@RequestParam(required = false, defaultValue = "") String country,
                                             @RequestParam(required = false, defaultValue = "") String area, @RequestParam int step) throws InterruptedException, ParseException {

        TimeInterval timer = DateUtil.timer();
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        // 1.获取road数据
        // List<Road> roadList = roadService.list();
        // String searchSql = "limit " + step + " offset 0";
        // List<Road> roadList = roadService.lambdaQuery().last(searchSql).list();
        Integer listSize = roadService.count();
        // List<Road> roadList = roadService.lambdaQuery().eq(Road::getOsmId,"929532963").list();
        // 2.道路截断
        // 2.1获取版本号
        Map<String, Object> map = roadBreakService.getMap(new QueryWrapper<RoadBreak>().select("max(version) as maxval"));
        Long version;
        if (map == null) {
            version = 1L;
        } else {
            version = (Long) map.get("maxval") + 1;
        }
        int fieldNum = BeanUtil.beanToMap(new RoadBreak()).keySet().size();
        int batchSize = 32767 / fieldNum;
        log.info("batchInsert size is {}", batchSize);
        CountDownLatch downLatch = new CountDownLatch(listSize / batchSize + 1);

        for (int i = 0; i <= listSize / batchSize; i++) {

            List<Road> batchList = roadService.lambdaQuery().orderByDesc(Road::getId).last("limit " + batchSize + " offset " + i * batchSize).list();
            roadBreakService.roadBreak(batchList, downLatch, version, country, area);
        }
        downLatch.await();
        // roadBreakMapper.batchInsertRoadBreak(roadBreakList);
        log.info("道路截断入库耗时：{}s", timer.intervalSecond());
        return ResponseResult.OK(true, true);
    }

    @ApiOperation(value = "osm link break")
    @PostMapping("/linkBreak")
    public ResponseResult<Boolean> linkBreak(@RequestParam(required = false, defaultValue = "") String countryOsm,
                                             @RequestParam(required = false, defaultValue = "") String area, @RequestParam int step) throws InterruptedException, ParseException {

        TimeInterval timer = DateUtil.timer();
        if (!countryOsm.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(countryOsm, false));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        log.info("current ds is " + DynamicDataSourceContextHolder.peek());
        // 1.获取经过preProcess编译后的osm，link数据
        int listSize = linkMService.count();
        // 2.道路截断
        // 2.1获取版本号
        Map<String, Object> map = linkBreakService.getMap(new QueryWrapper<LinkBreak>().select("max(version) as maxval"));
        Long version;
        if (map == null) {
            version = 1L;
        } else {
            version = (Long) map.get("maxval") + 1;
        }
        int fieldNum = BeanUtil.beanToMap(new LinkBreak()).keySet().size();
        int batchSize = 32767 / fieldNum;
        log.info("batchInsert size is {}", batchSize);
        CountDownLatch downLatch = new CountDownLatch(listSize / batchSize + 1);

        for (int i = 0; i <= listSize / batchSize; i++) {

            List<LinkM> batchList = linkMService.lambdaQuery().orderByDesc(LinkM::getLinkId).last("limit " + batchSize + " offset " + i * batchSize).list();
            linkBreakService.linkBreak(batchList, downLatch, version, countryOsm, area);
        }
        downLatch.await();
        // roadBreakMapper.batchInsertRoadBreak(roadBreakList);
        log.info("道路截断入库耗时：{}s", timer.intervalSecond());
        return ResponseResult.OK(true, true);
    }

    @ApiOperation(value = "osm link match with here link")
    @PostMapping("/linkMatch")
    public ResponseResult<Boolean> linkMatch(@RequestParam(required = false, defaultValue = "") String country,
                                             @RequestParam(required = false, defaultValue = "") String countryOsm,
                                             @RequestParam(required = false, defaultValue = "") String area,
                                             @RequestParam int step,
                                             @RequestParam int version) throws InterruptedException {

        TimeInterval timer = DateUtil.timer();
        if (!countryOsm.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(countryOsm, false));
        }
        // 1.多线程处理参数准备
        int count = linkBreakService.lambdaQuery().eq(LinkBreak::getVersion, version).count();
        int fieldNum = BeanUtil.beanToMap(new RoadMatchRes()).keySet().size();
        int batchSize = 32767 / fieldNum;
        log.info("batchInsert size is {}", batchSize);
        CountDownLatch countDownLatch = new CountDownLatch(count / batchSize + 1);

        // 2.过滤掉一些osm道路
        // List<String> kinds = CollUtil.newArrayList("living_street", "track", "bus_guideway","escape","raceway","busway","bridleway","corridor");
        List<String> kinds = CollUtil.newArrayList("track", "bus_guideway", "raceway", "busway", "bridleway", "corridor");

        for (int i = 0; i < count / batchSize + 1; i++) {
            if (!countryOsm.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(countryOsm, false));
            }
            List<LinkBreak> batchList = linkBreakService.lambdaQuery().notIn(LinkBreak::getKind, kinds).eq(LinkBreak::getVersion, version).orderByDesc(LinkBreak::getSubLinkId).last("limit " + batchSize + " offset " + i * batchSize).list();
            roadMatchResService.linkMatch(batchList, country, area, countDownLatch);
        }

        countDownLatch.await();

        log.info("匹配道路数据入库，耗时{}s", timer.intervalSecond());
        return ResponseResult.OK(true, true);

    }


    @ApiOperation(value = "alone road merge")
    @PostMapping("/aloneRoadMerge")
    public ResponseResult<Boolean> aloneRoadMerge(@RequestParam(required = false, defaultValue = "") String country,
                                                  @RequestParam(required = false, defaultValue = "") String area, @RequestParam int step) throws InterruptedException {
        TimeInterval timer = DateUtil.timer();
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        // 1.获取所有孤立道路数量
        // int count = roadMatchResService.getAloneMergeRoadNum();
        //// 2.多线程参数准备
        // CountDownLatch downLatch = new CountDownLatch(count / step + 1);
        // for (int i = 0; i < count / step; i++) {
        //    //List<RoadMatchRes> batchList = roadMatchResService.getAloneMergeRoad(step, i * step);
        //    // 测试
        //    List<RoadMatchRes> batchList = roadMatchResService.lambdaQuery().eq(RoadMatchRes::getOsmId, "817054291").list();
        //    try {
        //        linkService.aloneRoadMerge(batchList, downLatch, country, area);
        //    } catch (ParseException e) {
        //        e.printStackTrace();
        //    }
        //}
        // List<RoadMatchRes> roadList = roadMatchResService.lambdaQuery().eq(RoadMatchRes::getOsmId, "885829490").list();
        List<RoadMatchRes> roadList = roadMatchResService.getIndependentRoad();
        log.info("孤立道路数量{}", roadList.size());
        List<List<RoadMatchRes>> splitList = CollUtil.splitList(roadList, step);
        CountDownLatch downLatch = new CountDownLatch(splitList.size());
        // CountDownLatch downLatch= null;
        for (List<RoadMatchRes> matchResList : splitList) {
            try {
                linkMService.aloneRoadMerge(matchResList, downLatch, country, area);
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
        downLatch.await();
        log.info("孤立道路融合入库耗时：{}s", timer.intervalSecond());
        return ResponseResult.OK(true, true);
    }

    @ApiOperation(value = "alone link merge")
    @PostMapping("/aloneLinkMerge")
    public ResponseResult<Boolean> aloneLinkMerge(@RequestParam(required = false, defaultValue = "") String countryHere,
                                                  @RequestParam(required = false, defaultValue = "") String countryOsm,
                                                  @RequestParam(required = false, defaultValue = "") String area, @RequestParam int step) throws InterruptedException {
        TimeInterval timer = DateUtil.timer();
        if (!countryHere.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(countryHere, false));
        }
        List<RoadMatchRes> roadList = roadMatchResService.getIndependentRoad();
        log.info("孤立道路数量{}", roadList.size());
        List<List<RoadMatchRes>> splitList = CollUtil.splitList(roadList, step);
        CountDownLatch downLatch = new CountDownLatch(splitList.size());
        // CountDownLatch downLatch= null;
        for (List<RoadMatchRes> matchResList : splitList) {
            linkMService.aloneLinkMerge(matchResList, downLatch, countryHere, area, countryOsm);
        }
        downLatch.await();
        log.info("孤立道路融合入库耗时：{}s", timer.intervalSecond());
        return ResponseResult.OK(true, true);
    }

    @PostMapping(value = {"/processOsmNode"})
    public ResponseResult<Boolean> preProcessOsmNode(@RequestParam String filePath, @RequestParam String country) {
        File osmFile = new File(filePath);
        log.info("Starting to process OSM file: '" + osmFile + "'");
        TimeInterval timer = DateUtil.timer();
        DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        MybatisPlusConfig.myTableName.set("");

        try (OSMInput in = openOsmInputFile(osmFile)) {
            ReaderElement item;
            List<NodeM> nodeList = new ArrayList<>();
            int batchNum = 32767 / BeanUtil.beanToMap(new NodeM()).keySet().size();
            while ((item = in.getNext()) != null) {
                if (item.isType(ReaderElement.NODE)) {
                    NodeM nodeAdd = new NodeM();
                    final ReaderNode node = (ReaderNode) item;
                    nodeAdd.setNodeId(String.valueOf(node.getId()));
                    nodeAdd.setGeomwkt("POINT(" + node.getLon() + " " + node.getLat() + ")");
                    nodeAdd.setGeometry("SRID=4326;" + "POINT(" + node.getLon() + " " + node.getLat() + ")");
                    nodeAdd.setHllNodeid(UUID.randomUUID().toString());
                    nodeAdd.setUpDate(LocalDateTime.now());
                    nodeList.add(nodeAdd);
                    if (nodeList.size() == batchNum) {
                        log.info("nodeList size:{}", nodeList.size());
                        nodeMMapper.mysqlInsertOrUpdateBath(nodeList);
                        nodeList.clear();
                    }
                }
            }
            log.info("exceed nodeList size is:{}", nodeList.size());
            if (CollUtil.isNotEmpty(nodeList)) {
                nodeMMapper.mysqlInsertOrUpdateBath(nodeList);
            }
            log.info("preprocess osm node finished. cost time is {}s", timer.intervalSecond());
            ResponseResult<Boolean> responseResult = ResponseResult.OK(true, true);
            return responseResult;
        } catch (Exception ex) {
            // throw new RuntimeException("Problem while parsing file", ex);
            log.error("Problem while parsing file", ex);
            return ResponseResult.exceptionInfo(GlobalCodeEnum.GL_FAIL_999999, "OSM入库异常:" + ex, false);
        }
    }

    @PostMapping(value = {"/processOsmWay"})
    public ResponseResult<Boolean> preProcessOsmWay(@RequestParam String filePath, @RequestParam String country) {
        File osmFile = new File(filePath);
        log.info("Starting to process OSM file: '" + osmFile + "'");
        TimeInterval timer = DateUtil.timer();
        DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        MybatisPlusConfig.myTableName.set("");

        try (OSMInput in = openOsmInputFile(osmFile)) {
            String linkGeomWkt;
            ReaderElement item;
            List<LinkM> linkList = new ArrayList<>();
            Multimap<Long, Long> sLindIdMap = ArrayListMultimap.create();
            Multimap<Long, Long> eLindIdMap = ArrayListMultimap.create();
            Map<LongArrayList, Long> nodesIdLinkMap = new HashMap<>();
            Map<Long, List<Long>> connectIdsMap = new HashMap<>();
            int batchNum = 32767 / BeanUtil.beanToMap(new LinkM()).keySet().size();
            while ((item = in.getNext()) != null) {
                if (item.isType(ReaderElement.WAY)) {
                    LinkM linkAdd = new LinkM();
                    final ReaderWay way = (ReaderWay) item;
                    if (way.getTag("highway") == null) {
                        continue;
                    }
                    // link_id
                    linkAdd.setLinkId(String.valueOf(way.getId()));
                    // hll_s_nodeid
                    linkAdd.setHllSNid(String.valueOf(way.getNodes().get(0)));
                    // hll_e_nodeid
                    linkAdd.setHllENid(String.valueOf(way.getNodes().get(way.getNodes().size() - 1)));
                    linkAdd.setHllLinkid(UUID.randomUUID().toString());
                    // updatetime
                    linkAdd.setUpDate(LocalDateTime.now());
                    // nameCho
                    linkAdd.setNameChO(way.getTag("name"));
                    // nameCha
                    linkAdd.setNameChA(way.getTag("alt_name"));
                    // kind
                    linkAdd.setKind(way.getTag("highway"));
                    // dir
                    if ("yes".equals(way.getTag("oneway"))) {
                        linkAdd.setDir("2");
                    } else if ("no".equals(way.getTag("oneway"))) {
                        linkAdd.setDir("1");
                    } else if ("reversible".equals(way.getTag("oneway"))) {
                        linkAdd.setDir("3");
                    } else {
                        linkAdd.setDir("1");
                    }
                    // link releated nodes
                    linkAdd.setMemo(way.getNodes().toString());
                    int s = way.getNodes().size();
                    linkGeomWkt = "";
                    List<String> nodeIds = new ArrayList<>();
                    for (int index = 0; index < s; index++) {
                        nodeIds.add(String.valueOf(way.getNodes().get(index)));
                    }
                    List<NodeM> nodes = nodeMMapper.selectList(Wrappers.<NodeM>lambdaQuery().in(NodeM::getNodeId, nodeIds));
                    Map<String, NodeM> idNodeMap = nodes.stream().collect(Collectors.toMap(NodeM::getNodeId, o -> o));
                    for (int index = 0; index < s; ++index) {
                        linkGeomWkt = linkGeomWkt + "," + idNodeMap.get(String.valueOf(way.getNodes().get(index))).getGeomwkt().replace("POINT(", "").replace(")", "");
                    }
                    // linkGeomWkt
                    linkGeomWkt = "MULTILINESTRING((" + linkGeomWkt.replaceFirst(",", "") + "))";
                    // 可能存在为一个点的错误数据，需要过滤掉
                    if (StrUtil.count(linkGeomWkt, " ") == 1) {
                        continue;
                    }
                    // log.info("linkGeomWkt is:"+linkGeomWkt);
                    linkAdd.setGeomwkt(linkGeomWkt);
                    // linkGeom
                    linkAdd.setGeometry("SRID=4326;" + linkGeomWkt);
                    linkList.add(linkAdd);
                    sLindIdMap.put(way.getNodes().get(0), way.getId());
                    eLindIdMap.put(way.getNodes().get(way.getNodes().size() - 1), way.getId());
                    nodesIdLinkMap.put(way.getNodes(), way.getId());
                    if (linkList.size() == batchNum) {
                        log.info("linkList size is:{}", linkList.size());
                        linkMMapper.mysqlInsertOrUpdateBath(linkList);
                        linkList.clear();
                    }

                }
            }

            for (LongArrayList arrayList : nodesIdLinkMap.keySet()
            ) {
                List<Long> conectedIdsList = new ArrayList<>();
                for (LongCursor nodeId : arrayList
                ) {
                    if (eLindIdMap.get(nodeId.value) != null && eLindIdMap.get(nodeId.value).size() > 0) {
                        conectedIdsList.addAll(eLindIdMap.get(nodeId.value));
                    }
                    if (sLindIdMap.get(nodeId.value) != null && sLindIdMap.get(nodeId.value).size() > 0) {
                        conectedIdsList.addAll(sLindIdMap.get(nodeId.value));
                    }
                }
                connectIdsMap.put(nodesIdLinkMap.get(arrayList), conectedIdsList);
            }

            if (CollUtil.isNotEmpty(linkList)) {
                log.info("exceed linklist size is:" + linkList.size());
                // log.info(nf(tmpRelationCounter) + " (preprocess), osmlink:" + nf((linkList.size()))
                //         + ", " + Helper.getMemInfo());
                linkMMapper.mysqlInsertOrUpdateBath(linkList);
            }

            // process need to update connected link
            List<LinkM> linkUpdateList = new ArrayList<>();
            for (Long linkid : connectIdsMap.keySet()
            ) {
                LinkM linkUpdate = new LinkM();
                linkUpdate.setLinkId(linkid.toString());
                linkUpdate.setPreLaunch(connectIdsMap.get(linkid).toString());
                linkUpdate.setUpDate(LocalDateTime.now());
                linkUpdateList.add(linkUpdate);
                log.info("update connected link id is:" + linkid + ",preLaunch is:" + connectIdsMap.get(linkid));
            }
            // update connected link
            List<List<LinkM>> listUpdatePatitions = Lists.partition(linkUpdateList, 500);
            for (int i = 0; i < listUpdatePatitions.size(); i++) {
                linkMMapper.mysqlUpdateBath(listUpdatePatitions.get(i));
            }
            log.info("update connected link finished,num is:" + connectIdsMap.size());
            log.info("update connected link finished,cost time is {}s", timer.intervalSecond());
            return ResponseResult.OK(true, true);

        } catch (Exception ex) {
            // throw new RuntimeException("Problem while parsing file", ex);
            log.error("Problem while parsing file", ex);
            return ResponseResult.exceptionInfo(GlobalCodeEnum.GL_FAIL_999999, "OSM入库异常:" + ex, false);
        }
    }

    /**
     * Preprocessing of OSM file to select nodes which are used for highways. This allows a more
     * compact graph data structure.
     */
    @PostMapping("/processOsm")
    public ResponseResult<Boolean> preProcess(@RequestParam String filePath, @RequestParam String country) {
        // String file1 = "/Users/<USER>/Downloads/vietnam-latest.osm.pbf";
        // String file1 = "/Users/<USER>/Downloads/map.osm";
        File osmFile = new File(filePath);
        // String file1 = "/Users/<USER>/Downloads/map (1).osm";
        // File osmFile = new File(file1);
        log.info("Starting to process OSM file: '" + osmFile + "'");
        TimeInterval timer = DateUtil.timer();
        DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
        MybatisPlusConfig.myTableName.set("");

        try (OSMInput in = openOsmInputFile(osmFile)) {
            long tmpWayCounter = 1;
            long tmpRelationCounter = 1;
            ReaderElement item;
            List<NodeM> nodeList = new ArrayList<>();
            Map<Long, NodeM> idNodeMap = new HashMap<>();
            List<LinkM> linkList = new ArrayList<>();
            Map<Long, LinkM> idLinkMap = new HashMap<>();
            List<RelationM> relationList = new ArrayList<>();
            Map<Long, RelationM> idRelationMap = new HashMap<>();
            Multimap<Long, Long> sLindIdMap = ArrayListMultimap.create();
            Multimap<Long, Long> eLindIdMap = ArrayListMultimap.create();
            // link releated nodes map
            Map<LongArrayList, Long> nodesIdLinkMap = new HashMap<>();
            // link connected links map(one step)
            Map<Long, List<Long>> connectIdsMap = new HashMap<>();
            while ((item = in.getNext()) != null) {
                if (item.isType(ReaderElement.NODE)) {
//                    if(nodeList.size()>1000){
//                        continue;
//                    }
                    NodeM nodeAdd = new NodeM();
                    final ReaderNode node = (ReaderNode) item;
                    nodeAdd.setNodeId(String.valueOf(node.getId()));
                    nodeAdd.setGeomwkt("POINT(" + node.getLon() + " " + node.getLat() + ")");
                    nodeAdd.setGeometry("SRID=4326;" + "POINT(" + node.getLon() + " " + node.getLat() + ")");
                    nodeAdd.setHllNodeid(UUID.randomUUID().toString());
                    nodeAdd.setUpDate(LocalDateTime.now());
                    nodeList.add(nodeAdd);
                    idNodeMap.put(node.getId(), nodeAdd);
                    if (nodeList.size() == 500000) {
                        log.info("nodelist size is:" + nodeList.size());
                        log.info(nf(tmpRelationCounter) + " (preprocess), osmnode:" + nf((nodeList.size()))
                                + ", " + Helper.getMemInfo());
                        List<List<NodeM>> listPartitions = Lists.partition(nodeList, 1000);
                        for (int i = 0; i < listPartitions.size(); i++) {
                            nodeMMapper.mysqlInsertOrUpdateBath(listPartitions.get(i));
                        }
                        nodeList = new ArrayList<>();
                    }
                } else if (item.isType(ReaderElement.WAY)) {
//                    log.info("process node completed.node size is:"+nodeList.size());
//                    if(linkList.size()>100){
//                        continue;
//                    }
                    LinkM linkAdd = new LinkM();
                    final ReaderWay way = (ReaderWay) item;
                    if (way.getTag("highway") == null) {
                        continue;
                    }
                    // link_id
                    linkAdd.setLinkId(String.valueOf(way.getId()));
                    // hll_s_nodeid
                    linkAdd.setHllSNid(String.valueOf(way.getNodes().get(0)));
                    // hll_e_nodeid
                    linkAdd.setHllENid(String.valueOf(way.getNodes().get(way.getNodes().size() - 1)));
                    linkAdd.setHllLinkid(UUID.randomUUID().toString());
                    // updatetime
                    linkAdd.setUpDate(LocalDateTime.now());
                    // nameCho
                    linkAdd.setNameChO(way.getTag("name"));
                    // nameCha
                    linkAdd.setNameChA(way.getTag("alt_name"));
                    // kind
                    linkAdd.setKind(way.getTag("highway"));
                    // dir
                    if ("yes".equals(way.getTag("oneway"))) {
                        linkAdd.setDir("2");
                    } else if ("no".equals(way.getTag("oneway"))) {
                        linkAdd.setDir("1");
                    } else if ("reversible".equals(way.getTag("oneway"))) {
                        linkAdd.setDir("3");
                    } else {
                        linkAdd.setDir("1");
                    }
                    // link releated nodes
                    linkAdd.setMemo(way.getNodes().toString());
                    int s = way.getNodes().size();
                    String linkGeomWkt = "";
                    for (int index = 0; index < s; index++) {
                        if (idNodeMap.get(way.getNodes().get(index)) == null) {
                            log.info("nodes:" + way.getNodes());
                            log.info("nodes id:" + way.getNodes().get(index));
                            log.info("node map contains key:" + idNodeMap.containsKey(way.getNodes().get(index)));
                            log.info("idNodeMap can not get relevant node,way id is: {},size is {},index is {}", way.getId(), s, index);
                            continue;
                        }
                        linkGeomWkt += "," + idNodeMap.get(way.getNodes().get(index)).getGeomwkt().replace("POINT(", "").replace(")", "");
                    }
                    if (StrUtil.isEmpty(linkGeomWkt)) {
                        continue;
                    }
                    // linkGeomWkt
                    linkGeomWkt = "MULTILINESTRING((" + linkGeomWkt.replaceFirst(",", "") + "))";
                    // 可能存在为一个点的错误数据，需要过滤掉
                    if (StrUtil.count(linkGeomWkt, " ") == 1) {
                        continue;
                    }
                    // log.info("linkGeomWkt is:"+linkGeomWkt);
                    linkAdd.setGeomwkt(linkGeomWkt);
                    // linkGeom
                    linkAdd.setGeometry("SRID=4326;" + linkGeomWkt);
                    linkList.add(linkAdd);
                    sLindIdMap.put(way.getNodes().get(0), way.getId());
                    eLindIdMap.put(way.getNodes().get(way.getNodes().size() - 1), way.getId());
                    nodesIdLinkMap.put(way.getNodes(), way.getId());
                    // log.info("process link completed.link size is:"+linkList.size());
                    // idLinkMap.put(way.getId(),linkAdd);
                    if (linkList.size() == 500000) {
                        log.info("linklist size is:" + linkList.size());
                        List<List<LinkM>> listLinkPartitions = Lists.partition(linkList, 500);
                        for (int i = 0; i < listLinkPartitions.size(); i++) {
                            linkMMapper.mysqlInsertOrUpdateBath(listLinkPartitions.get(i));
                        }
                        linkList = new ArrayList<>();
                    }
                } else if (item.isType(ReaderElement.RELATION)) {
                    final ReaderRelation relation = (ReaderRelation) item;
                    if (!relation.isMetaRelation() && relation.hasTag("type", "route"))
                    // prepareWaysWithRelationInfo(relation);
                    {
                        if (relation.hasTag("type", "restriction")) {
                            // prepareRestrictionRelation(relation);
                        }
                    }

                    if (++tmpRelationCounter % 100_000 == 0) {

                    }
                } else if (item.isType(ReaderElement.FILEHEADER)) {
                    final OSMFileHeader fileHeader = (OSMFileHeader) item;
                }
            }
            for (LongArrayList arrayList : nodesIdLinkMap.keySet()
            ) {
                List<Long> conectedIdsList = new ArrayList<>();
                for (LongCursor nodeId : arrayList
                ) {
                    if (eLindIdMap.get(nodeId.value) != null && eLindIdMap.get(nodeId.value).size() > 0) {
                        conectedIdsList.addAll(eLindIdMap.get(nodeId.value));
                    }
                    if (sLindIdMap.get(nodeId.value) != null && sLindIdMap.get(nodeId.value).size() > 0) {
                        conectedIdsList.addAll(sLindIdMap.get(nodeId.value));
                    }
                }
                connectIdsMap.put(nodesIdLinkMap.get(arrayList), conectedIdsList);
            }
            log.info("nodelist size is:" + nodeList.size());
            List<List<NodeM>> listPartitions = Lists.partition(nodeList, 1000);
            for (int i = 0; i < listPartitions.size(); i++) {
                nodeMMapper.mysqlInsertOrUpdateBath(listPartitions.get(i));
            }
            log.info("linklist size is:" + linkList.size());
            List<List<LinkM>> listLinkPartitions = Lists.partition(linkList, 500);
            for (int i = 0; i < listLinkPartitions.size(); i++) {
                linkMMapper.mysqlInsertOrUpdateBath(listLinkPartitions.get(i));
            }
            // process need to update connected link
            List<LinkM> linkUpdateList = new ArrayList<>();
            for (Long linkid : connectIdsMap.keySet()
            ) {
                LinkM linkUpdate = new LinkM();
                linkUpdate.setLinkId(linkid.toString());
                linkUpdate.setPreLaunch(connectIdsMap.get(linkid).toString());
                linkUpdate.setUpDate(LocalDateTime.now());
                linkUpdateList.add(linkUpdate);
                log.info("update connected link id is:" + linkid + ",preLaunch is:" + connectIdsMap.get(linkid));
            }
            // update connected link
            List<List<LinkM>> listUpdatePatitions = Lists.partition(linkUpdateList, 500);
            for (int i = 0; i < listUpdatePatitions.size(); i++) {
                linkMMapper.mysqlUpdateBath(listUpdatePatitions.get(i));
            }
            log.info("update connected link finished,num is:" + connectIdsMap.size());
            log.info("update connected link finished,cost time is {}s", timer.intervalSecond());
            // linkMapper.mysqlInsertOrUpdateBath(linkList);
//            linkService.saveOrUpdateBatch(linkList);
            return ResponseResult.OK(true, true);
        } catch (Exception ex) {
            // throw new RuntimeException("Problem while parsing file", ex);
            log.error("Problem while parsing file", ex);
            return ResponseResult.exceptionInfo(GlobalCodeEnum.GL_FAIL_999999, "OSM入库异常:" + ex, false);
        }
    }

    protected OSMInput openOsmInputFile(File osmFile) throws XMLStreamException, IOException {
        return new OSMInputFile(osmFile).setWorkerThreads(2).open();
    }

    @ApiOperation(value = "branch road merge")
    @PostMapping("/branchRoadMerge")
    public ResponseResult<Boolean> branchRoadMerge(@RequestParam(required = false, defaultValue = "") String country,
                                                   @RequestParam(required = false, defaultValue = "") String area, @RequestParam int step) throws InterruptedException {
        TimeInterval timer = DateUtil.timer();
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        // 1.获取支路长度有变化数据源
        int num = roadMatchResService.getBranchRoadChangedNum();
        // int num = 1;
        log.info("wait to be processed num is {}", num);
        int size = num % step == 0 ? num / step : num / step + 1;
        CountDownLatch downLatch = new CountDownLatch(size);
        // 2.多线程处理支路融合
        for (int i = 0; i < size; i++) {
            List<String> resList = roadMatchResService.getBranchRoadChangedId(step, i * step);
            // List<String> resList = CollUtil.newArrayList("137554718");
            try {
                // linkService.branchRoadMerge(resList, country, area, downLatch);
                linkMService.branchRoadMergeOfMove(resList, country, area, downLatch);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        downLatch.await();
        log.info("支路长度变化场景融合入库耗时：{}s", timer.intervalSecond());
        return ResponseResult.OK(true, true);
    }

    @ApiOperation(value = "branch link merge")
    @PostMapping("/branchLinkMerge")
    public ResponseResult<Boolean> branchLinkMerge(@RequestParam(required = false, defaultValue = "") String country, @RequestParam(required = false, defaultValue = "") String countryOsm,
                                                   @RequestParam(required = false, defaultValue = "") String area, @RequestParam int step) throws InterruptedException {
        TimeInterval timer = DateUtil.timer();
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        // 1.获取支路长度有变化数据源
        int num = roadMatchResService.getBranchRoadChangedNum();
        // int num = 1;
        log.info("wait to be processed num is {}", num);
        int size = num % step == 0 ? num / step : num / step + 1;
        CountDownLatch downLatch = new CountDownLatch(size);
        // 2.多线程处理支路融合
        for (int i = 0; i < size; i++) {
            List<String> resList = roadMatchResService.getBranchRoadChangedId(step, i * step);
            // List<String> resList = CollUtil.newArrayList("137554718");
            try {
                // linkService.branchRoadMerge(resList, country, area, downLatch);
                linkMService.branchLinkMergeOfMove(resList, country, area, countryOsm, downLatch);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        downLatch.await();
        log.info("支路长度变化场景融合入库耗时：{}s", timer.intervalSecond());
        return ResponseResult.OK(true, true);
    }

    @ApiOperation(value = "tree branch road merge")
    @PostMapping("/treeBranchRoadMerge")
    public ResponseResult<Boolean> treeBranchRoadMerge(@RequestParam(required = false, defaultValue = "") String countryHere,
                                                       @RequestParam(required = false, defaultValue = "") String countryOsm,
                                                       @RequestParam(required = false, defaultValue = "") String area, @RequestParam int step) throws InterruptedException {
        TimeInterval timer = DateUtil.timer();
        if (!countryHere.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(countryHere, false));
        }
        // 1.获取符合该场景类型的osm道路数量
        // int num = roadMatchResService.getTreeBranchMergeNum();
        int num = roadMatchResService.getAloneMergeRoadNum();
        // int num = 1;
        log.info("wait to be processed num is {}", num);
        int size = num % step == 0 ? num / step : num / step + 1;
        CountDownLatch downLatch = new CountDownLatch(size);
        for (int i = 0; i < size; i++) {
            // List<String> resList = roadMatchResService.getTreeBranchMergeOsmId(step, i * step);
            List<RoadMatchRes> matchList = roadMatchResService.getAloneMergeRoad(step, i * step);
            // List<RoadMatchRes> matchList = roadMatchResService.lambdaQuery().in(RoadMatchRes::getOsmId, CollUtil.newArrayList("918504102", "918504100")).list();
            // List<RoadMatchRes> matchList = roadMatchResService.lambdaQuery().in(RoadMatchRes::getOsmId, CollUtil.newArrayList("958236802","958236803","866379713","866379710")).list();
            // List<String> resList = matchList.stream().map(RoadMatchRes::getOsmId).collect(Collectors.toList());
            // List<String> resList = CollUtil.newArrayList("805903596");
            linkMService.treeBranchMerge(matchList, countryHere, countryOsm, area, downLatch);
        }
        downLatch.await();
        log.info("树状支路场景融合入库耗时：{}s", timer.intervalSecond());
        return ResponseResult.OK(true, true);
    }

    @ApiOperation(value = "tree branch road merge")
    @PostMapping("/treeBranchRoadAdjoinBreak")
    public ResponseResult<Boolean> treeBranchRoadAdjoinBreak(@RequestParam(required = false, defaultValue = "") String country,
                                                             @RequestParam(required = false, defaultValue = "") String area, @RequestParam int step) throws InterruptedException {
        TimeInterval timer = DateUtil.timer();
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        // 针对融合入库第三类场景的后续处理
        // 1）道路打断  2）relation，rule同步维护
        // 1.筛选已融合到link表中，cp字段不为null的道路数据
        List<LinkM> trunkLinks = linkMService.lambdaQuery().isNotNull(LinkM::getAdopt).eq(LinkM::getStatus, 0).list();
        // 根据主干id所关联的原始link来分组
        Map<String, List<LinkM>> listMap = trunkLinks.stream().collect(Collectors.groupingBy(s -> s.getAdopt()));
        Set<String> strings = listMap.keySet();
        List<List<String>> trunkIds = CollUtil.split(strings, step);
        //--测试code
        // List<List<String>> trunkIds = new ArrayList<>();
        // ArrayList<String> list1 = CollUtil.newArrayList("1208668359");
        ////ArrayList<String> list2 = CollUtil.newArrayList("1175678929");
        // trunkIds.add(list1);
        ////trunkIds.add(list2);
        //--
        CountDownLatch downLatch = new CountDownLatch(trunkIds.size());
        for (List<String> ids : trunkIds) {
            try {
                linkMService.roadAdjoinBreak(ids, listMap, country, area, downLatch);
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
        downLatch.await();
        log.info("树状支路场景道路打断耗时：{}s", timer.intervalSecond());
        return ResponseResult.OK(true, true);
    }

    @ApiOperation(value = "prepare road merge case3")
    @PostMapping("/prepareRoadstoMergeCase3")
    public ResponseResult<Boolean> prepareRoadstoMergeCase3(@RequestParam(required = false, defaultValue = "") String country,
                                                            @RequestParam(required = false, defaultValue = "") String area, @RequestParam int step) throws InterruptedException, SQLException {
        TimeInterval timer = DateUtil.timer();
        // if (!country.isEmpty()) {
        DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry("vnm-o", false));
        //}
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        int linkNum = linkMService.count();
        for (int i = 0; i < linkNum / step; i++) {
            List<LinkM> streetsListi = linkMService.lambdaQuery()
                    .orderByDesc(LinkM::getLinkId).last("limit " + step + " offset " + i * step).list();
            log.info("process start limit " + step + " offset " + i * step);
            if (streetsListi.size() > 0) {
                // linkSw2021q133Service.linkConvert(streetsListi,nodeIdSet,nodeList);
                linkMService.prepareLinkstoMerge(streetsListi, area, country);
            }
        }
        return ResponseResult.OK(true, true);
    }


    @GetMapping("/processFormway")
    public ResponseResult<String> processFormway(String country, String area) {

        log.info("开始更新formway,country is {},area is {}", country, area);
        TimeInterval timer = DateUtil.timer();
        try {
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            int fieldNum = BeanUtil.beanToMap(new LinkM()).keySet().size();
            int batchSize = 32767 / fieldNum;
            int totalNum = linkMService.count();
            int loop = totalNum % batchSize == 0 ? totalNum / batchSize : totalNum / batchSize + 1;
            CountDownLatch countDownLatch = new CountDownLatch(loop);
            for (int i = 0; i < loop; i++) {
                if (!country.isEmpty()) {
                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                }
                if (!area.isEmpty()) {
                    MybatisPlusConfig.myTableName.set("_" + area);
                } else {
                    MybatisPlusConfig.myTableName.set("");
                }

                List<LinkM> batchLinkMList = linkMService.lambdaQuery().orderByDesc(LinkM::getHllLinkid).last("limit " + batchSize + " offset " + i * batchSize).list();
                log.info("process start limit " + batchSize + " offset " + i * batchSize);
                linkMService.processFormway(country, area, countDownLatch, batchLinkMList);
            }
            countDownLatch.await();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        log.info("更新formway完成,耗时:{}s,country is {},area is {}", timer.intervalSecond(), country, area);
        return ResponseResult.OK("formway更新成功", true);
    }


    @GetMapping("city")
    public ResponseResult<String> findTimeZoneByCities(String country, Integer areas, String cities) {
        // 1.基于here源库来操作
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
        }
        List<String> cityList = StrUtil.split(cities, ',');
        Map<String, String> resMap = new LinkedHashMap<>();
        if (areas > 0) {
            for (String city : cityList) {
                for (int i = 0; i < areas; i++) {
                    MybatisPlusConfig.myTableName.set("_area" + (i + 1));
                    // 2.查询城市对应的area_id
                    List<Mtdarea> mtdareas = mtdareaService.lambdaQuery().eq(Mtdarea::getAreaName, StrUtil.trim(city).toUpperCase()).or().eq(Mtdarea::getAreaNmTr, StrUtil.trim(city).toUpperCase()).list();
                    if (CollUtil.isNotEmpty(mtdareas)) {
                        Mtdarea mtdarea = mtdareas.stream().min(Comparator.comparingInt(Mtdarea::getAdminLvl)).get();
                        resMap.put(city, mtdarea.getAreaId() + "");
                        // 3. 获取时区
                        getTimeZone(mtdarea, resMap, city);
                        if (resMap.get(city).contains("/")) {
                            break;
                        }
                    }
                }
            }
        } else if (areas == 0) {
            for (String city : cityList) {
                MybatisPlusConfig.myTableName.set("");
                // 2.查询城市对应的area_id
                List<Mtdarea> mtdareas = mtdareaService.lambdaQuery().eq(Mtdarea::getAreaName, StrUtil.trim(city).toUpperCase()).or().eq(Mtdarea::getAreaNmTr, StrUtil.trim(city).toUpperCase()).list();
                if (CollUtil.isNotEmpty(mtdareas)) {
                    Mtdarea mtdarea = mtdareas.stream().min(Comparator.comparingInt(Mtdarea::getAdminLvl)).get();
                    resMap.put(city, mtdarea.getAreaId() + "");
                    // 3. 获取时区
                    getTimeZone(mtdarea, resMap, city);
                }
            }
        }

        return ResponseResult.OK(resMap.toString(), true);
    }

    private void getTimeZone(Mtdarea mtdarea, Map<String, String> resMap, String city) {
        Integer adminLvl = mtdarea.getAdminLvl();
        setTimeZone(adminLvl, resMap, mtdarea, city);
    }

    private boolean setTimeZone(Integer adminLvl, Map<String, String> resMap, Mtdarea mtdarea, String city) {
        for (Integer l = adminLvl; l > 0; l--) {
            if (l == 1) {
                List<Mtdarea> list = mtdareaService.lambdaQuery().eq(Mtdarea::getAreacode1, mtdarea.getAreacode1()).eq(Mtdarea::getAdminLvl, l).list();
                if (CollUtil.isNotEmpty(list)) {
                    List<Mtddst> mtddstList1 = mtddstService.lambdaQuery().eq(Mtddst::getAreaId, list.get(0).getAreaId()).list();
                    if (CollUtil.isNotEmpty(mtddstList1)) {
                        Mtddst mtddst = mtddstList1.get(0);
                        if (mtddst.getTimeZone() != null) {
                            String res = resMap.get(city) + "/" + mtddst.getTimeZone();
                            resMap.put(city, res);
                            return true;
                        }
                    }
                }
            } else if (l == 2) {
                List<Mtdarea> list = mtdareaService.lambdaQuery().eq(Mtdarea::getAreacode1, mtdarea.getAreacode1())
                        .eq(Mtdarea::getAreacode2, mtdarea.getAreacode2()).eq(Mtdarea::getAdminLvl, l).list();
                if (CollUtil.isNotEmpty(list)) {
                    List<Mtddst> mtddstList1 = mtddstService.lambdaQuery().eq(Mtddst::getAreaId, list.get(0).getAreaId()).list();
                    if (CollUtil.isNotEmpty(mtddstList1)) {
                        Mtddst mtddst = mtddstList1.get(0);
                        if (mtddst.getTimeZone() != null) {
                            String res = resMap.get(city) + "/" + mtddst.getTimeZone();
                            resMap.put(city, res);
                            return true;
                        }
                    }
                }
            } else if (l == 3) {
                List<Mtdarea> list = mtdareaService.lambdaQuery().eq(Mtdarea::getAreacode1, mtdarea.getAreacode1())
                        .eq(Mtdarea::getAreacode2, mtdarea.getAreacode2())
                        .eq(Mtdarea::getAreacode3, mtdarea.getAreacode3())
                        .eq(Mtdarea::getAdminLvl, l).list();
                if (CollUtil.isNotEmpty(list)) {
                    List<Mtddst> mtddstList1 = mtddstService.lambdaQuery().eq(Mtddst::getAreaId, list.get(0).getAreaId()).list();
                    if (CollUtil.isNotEmpty(mtddstList1)) {
                        Mtddst mtddst = mtddstList1.get(0);
                        if (mtddst.getTimeZone() != null) {
                            String res = resMap.get(city) + "/" + mtddst.getTimeZone();
                            resMap.put(city, res);
                            return true;
                        }
                    }
                }
            } else if (l == 4) {
                List<Mtdarea> list = mtdareaService.lambdaQuery().eq(Mtdarea::getAreacode1, mtdarea.getAreacode1())
                        .eq(Mtdarea::getAreacode2, mtdarea.getAreacode2())
                        .eq(Mtdarea::getAreacode3, mtdarea.getAreacode3())
                        .eq(Mtdarea::getAreacode4, mtdarea.getAreacode4())
                        .eq(Mtdarea::getAdminLvl, l).list();
                if (CollUtil.isNotEmpty(list)) {
                    List<Mtddst> mtddstList1 = mtddstService.lambdaQuery().eq(Mtddst::getAreaId, list.get(0).getAreaId()).list();
                    if (CollUtil.isNotEmpty(mtddstList1)) {
                        Mtddst mtddst = mtddstList1.get(0);
                        if (mtddst.getTimeZone() != null) {
                            String res = resMap.get(city) + "/" + mtddst.getTimeZone();
                            resMap.put(city, res);
                            return true;
                        }
                    }
                }
            } else if (l == 5) {
                List<Mtdarea> list = mtdareaService.lambdaQuery().eq(Mtdarea::getAreacode1, mtdarea.getAreacode1())
                        .eq(Mtdarea::getAreacode2, mtdarea.getAreacode2())
                        .eq(Mtdarea::getAreacode3, mtdarea.getAreacode3())
                        .eq(Mtdarea::getAreacode4, mtdarea.getAreacode4())
                        .eq(Mtdarea::getAreacode5, mtdarea.getAreacode5())
                        .eq(Mtdarea::getAdminLvl, l).list();
                if (CollUtil.isNotEmpty(list)) {
                    List<Mtddst> mtddstList1 = mtddstService.lambdaQuery().eq(Mtddst::getAreaId, list.get(0).getAreaId()).list();
                    if (CollUtil.isNotEmpty(mtddstList1)) {
                        Mtddst mtddst = mtddstList1.get(0);
                        if (mtddst.getTimeZone() != null) {
                            String res = resMap.get(city) + "/" + mtddst.getTimeZone();
                            resMap.put(city, res);
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }

    @Resource
    MeshLinkTemplateMapper meshLinkTemplateMapper;

    @GetMapping("merge")
    public ResponseResult<String> testMergeRoad() {
        DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry("hkg", true));
        List<MeshLinkTemplate> meshLinkTemplates = meshLinkTemplateMapper.selectList(null);

        Map<String, List<MeshLinkTemplate>> stringListMap = MergeUtil.identifyLinkNetworks(meshLinkTemplates);
        Map<String, List<String>> map = new HashMap<>();
        for (Map.Entry<String, List<MeshLinkTemplate>> stringListEntry : stringListMap.entrySet()) {
            map.put(stringListEntry.getKey(), stringListEntry.getValue().stream().map(MeshLinkTemplate::getHllLinkid).map(String::valueOf).collect(Collectors.toList()));
        }
        Optional<SimpleEntry<String, Integer>> maxEntry = map.entrySet().stream()
                .map(entry -> new SimpleEntry<>(entry.getKey(), entry.getValue().size()))
                .max(Entry.comparingByValue());

        maxEntry.ifPresent(entry -> {
            System.out.println("Key with largest List: " + entry.getKey());
            System.out.println("Size of the largest List: " + entry.getValue());
        });
        System.out.println();

        return ResponseResult.OK(map.toString(), true);
    }

    @ApiOperation(value = "Tomtom link convert")
    @PostMapping("/convertTT")
    public ResponseResult<Boolean> tomtomLinkConvert(@RequestParam(value = "step",
                                                             required = false,
                                                             defaultValue = "1") int step,
                                                     @RequestParam(value = "version",
                                                             required = false) String version,
                                                     @RequestParam(value = "iscompilenode",
                                                             required = false,
                                                             defaultValue = "true") boolean isCompileNode,
                                                     @RequestParam(value = "iscompiletranseng",
                                                             required = false,
                                                             defaultValue = "false") boolean isCompileTransEng,
                                                     @RequestParam(value = "area",
                                                             required = false,
                                                             defaultValue = "") String area,
                                                     @RequestParam(value = "country",
                                                             required = false,
                                                             defaultValue = "") String country)
            throws SQLException, ParseException, InterruptedException, IOException, NoSuchFieldException {

        // MybatisPlusConfig.zLevelsTableName.set("zlevels"+proFileEntity.getName());
        // MybatisPlusConfig.zLevelsTableName.set("zlevels_area1");
        // MybatisPlusConfig.myTableName.set("_"+area);
        // System.err.println("yyy:" + Thread.currentThread().getName());
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        TimeInterval timer = DateUtil.timer();

        Set<Integer> nodeIdSet = Collections.synchronizedSet(new TreeSet<>());
        List<NodeM> nodeList = Collections.synchronizedList(new ArrayList<>());

        Integer listSize = planetOsmLineService.count();
        CountDownLatch countDownLatch = new CountDownLatch(listSize / step + 1);
        log.info("The records to be transfered:" + listSize);
        for (int i = 0; i <= listSize / step; i++) {
//            if (i == 0) {
//                step = 100;
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            List<PlanetOsmLine> planetOsmLineListi = planetOsmLineService.lambdaQuery()
                    .orderByDesc(PlanetOsmLine::getOsmId).last("limit " + step + " offset " + i * step).list();
            List<PlanetOsmLine> planetOsmLineListProcess = planetOsmLineListi.stream()
                    .filter(line -> mapTTConfig.getRoadValues().contains(line.getHighway())) // 确保 null 安全，直接用 equals 对比
                    .collect(Collectors.toList());
            log.info("process start limit " + step + " offset " + i * step);
            log.info("planetOsmLineListProcess size is: " + planetOsmLineListProcess.size());
            if (planetOsmLineListProcess.size() > 0) {
                // linkSw2021q133Service.linkConvert(streetsListi,nodeIdSet,nodeList);
                linkMService.linkConvertTT(planetOsmLineListProcess, isCompileNode, isCompileTransEng, area, country, countDownLatch);
            }
//            }
        }
        countDownLatch.await();
        log.info("here streets convert to link node cost time is {}s", timer.intervalSecond());
        return ResponseResult.OK(true, true);
    }

    // 将十六进制字符串转换为字节数组
    private static byte[] hexToBytes(String hex) {
        int len = hex.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(hex.charAt(i), 16) << 4)
                    + Character.digit(hex.charAt(i + 1), 16));
        }
        return data;
    }

    @GetMapping("getTileWkt")
    public ResponseResult<String> getTileWkt(@RequestParam String country, @RequestParam String area) throws InterruptedException {

        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        List<Map<String, Object>> groupRes = linkMService.listMaps(Wrappers.<LinkM>lambdaQuery().select(LinkM::getTileId).ne(LinkM::getStatus, Const.STATUS_DELETED).groupBy(LinkM::getTileId));
        // List<Map<String, Object>> groupRes = linkMService.listMaps(
        //         new QueryWrapper<LinkM>()
        //                 .select("tile_id", "COUNT(*) AS cnt")          // 统计每个 tileId 的数量
        //                 .ne("status", Const.STATUS_DELETED)            // where status <> deleted
        //                 .groupBy("tile_id")                            // group by tile_id
        // );
        int count = groupRes.size();
        // groupRes.stream().forEach(res -> { tileIdList.addAll(res.values().forEach(v -> v.toString()).toList()); });
        List<String> tileIdList = groupRes.stream().flatMap(map -> map.values().stream()).map(Object::toString).collect(Collectors.toList());
        int batchSize = 32767 / BeanUtil.beanToMap(new TileView()).keySet().size();
        // System.out.println(tileIdList.size());
        List<List<String>> split = CollUtil.split(tileIdList, batchSize);
        CountDownLatch countDownLatch = new CountDownLatch(split.size());
        for (int i = 0; i < split.size(); i++) {
            linkMService.handleTileWkt(country, area, split.get(i), countDownLatch);
        }
        countDownLatch.await();

        return ResponseResult.OK("handle " + country + " succeed,tileId size is " + count, true);
    }
}