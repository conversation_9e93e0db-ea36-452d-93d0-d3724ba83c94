package com.hll.mapdataservice.business.api.road.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.hll.mapdataservice.business.interceptor.MybatisPlusConfig;
import com.hll.mapdataservice.business.third.InheritIDService;
import com.hll.mapdataservice.business.third.dto.InheritIDDTO;
import com.hll.mapdataservice.common.constant.Const;
import com.hll.mapdataservice.common.entity.*;
import com.hll.mapdataservice.common.mapper.*;
import com.hll.mapdataservice.common.service.ICdmsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hll.mapdataservice.common.utils.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-19
 */
@Service
@Slf4j
//@DS("db4")
public class CdmsServiceImpl extends ServiceImpl<CdmsMapper, Cdms> implements ICdmsService {

    @Resource
    HerePhaStreetsServiceImpl herePhaStreetsService;
    @Resource
    RdmsServiceImpl rdmsService;
    @Resource
    CndmodServiceImpl cndmodService;
    @Resource
    // RelationServiceImpl relationService;
    RelationMServiceImpl relationService;
    @Resource
    CdmsdtmodServiceImpl cdmsdtmodService;
    @Resource
    // RuleServiceImpl ruleService;
    RuleMServiceImpl ruleService;
    @Resource
    RelationMMapper relationMapper;
    @Resource
    RuleMMapper ruleMapper;
    @Resource
    InheritIDService inheritIDService;
    // @Resource
    // LinkMServiceImpl linkService;
    @Resource
    LinkMMapper linkMMapper;
    @Resource
    NodeMServiceImpl nodeMService;

    @Async("asyncTaskExecutor")
    public void releationConvert(CountDownLatch countDownLatch, List<Cdms> cdmsList, String area, String country) throws SQLException, InterruptedException {

        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }

        List<RelationM> relationList = new ArrayList<>();
        try {
            if (cdmsList.size() > 0) {
                for (Cdms cdms : cdmsList
                ) {
                    RelationM relationSw2021q133 = new RelationM();
                    // RELATIONID
                    relationSw2021q133.setRelationid(UUID.randomUUID().toString());

                    if (cdms.getCondType() == 1 || cdms.getCondType() == 4 || cdms.getCondType() == 9
                            || cdms.getCondType() == 11 || cdms.getCondType() == 16) {
                        // InLINK_ID
                        // 使用id继承，2022-01-20
                        // List<Long> inheritID1 = inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(cdms.getLinkId().toString())));
                        // relationSw2021q133.setInlinkId(inheritID1.get(0).toString());

                        if (!country.isEmpty()) {
                            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                        }
                        if (!area.isEmpty()) {
                            MybatisPlusConfig.myTableName.set("_" + area);
                        } else {
                            MybatisPlusConfig.myTableName.set("");
                        }
                        QueryWrapper<LinkM> queryWrapperIn = new QueryWrapper<>();
                        queryWrapperIn.eq("link_id", cdms.getLinkId().toString());
                        relationSw2021q133.setInlinkId(linkMMapper.selectOne(queryWrapperIn).getHllLinkid());


                        if (!country.isEmpty()) {
                            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
                        }
                        if (!area.isEmpty()) {
                            MybatisPlusConfig.myTableName.set("_" + area);
                        } else {
                            MybatisPlusConfig.myTableName.set("");
                        }
                        List<HerePhaStreets> herePhaStreetsList = herePhaStreetsService.lambdaQuery().eq(HerePhaStreets::getLinkId, cdms.getLinkId())
                                .list();
                        // NODEID
                        // 使用id继承，2022-01-20
                        if (herePhaStreetsList.size() > 0) {
                            if ("N".equals(cdms.getEndOfLk())) {
                                // List<Long> inheritID = inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(herePhaStreetsList.get(0).getNrefInId().toString())));
                                // relationSw2021q133.setNodeId(inheritID.get(0).toString());
                                if (!country.isEmpty()) {
                                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                                }
                                if (!area.isEmpty()) {
                                    MybatisPlusConfig.myTableName.set("_" + area);
                                } else {
                                    MybatisPlusConfig.myTableName.set("");
                                }

                                QueryWrapper<NodeM> querWrapperNode = new QueryWrapper<>();
                                querWrapperNode.eq("node_id", herePhaStreetsList.get(0).getNrefInId().toString());
                                relationSw2021q133.setNodeId(nodeMService.getOne(querWrapperNode).getHllNodeid());
                            }
                            if ("R".equals(cdms.getEndOfLk())) {
                                // List<Long> inheritID = inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(herePhaStreetsList.get(0).getRefInId().toString())));
                                // relationSw2021q133.setNodeId(inheritID.get(0).toString());
                                if (!country.isEmpty()) {
                                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                                }
                                if (!area.isEmpty()) {
                                    MybatisPlusConfig.myTableName.set("_" + area);
                                } else {
                                    MybatisPlusConfig.myTableName.set("");
                                }
                                QueryWrapper<NodeM> querWrapperNode = new QueryWrapper<>();
                                querWrapperNode.eq("node_id", herePhaStreetsList.get(0).getRefInId().toString());
                                relationSw2021q133.setNodeId(nodeMService.getOne(querWrapperNode).getHllNodeid());
                            }
                            // PASS_NUM
                            relationSw2021q133.setPassNum(herePhaStreetsList.get(0).getPhysLanes().toString());
                        }
                        // OutHll_LINK_ID
                        // 使用id继承，2022-01-20

                        if (!country.isEmpty()) {
                            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
                        }
                        if (!area.isEmpty()) {
                            MybatisPlusConfig.myTableName.set("_" + area);
                        } else {
                            MybatisPlusConfig.myTableName.set("");
                        }
                        List<Rdms> rdmsList = rdmsService.lambdaQuery().eq(Rdms::getCondId, cdms.getCondId()).list();
                        if (rdmsList.size() > 0) {
                            // List<Long> inheritID = inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(rdmsList.get(0).getManLinkid().toString())));
                            // relationSw2021q133.setOutlinkId(inheritID.get(0).toString());

                            if (!country.isEmpty()) {
                                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                            }
                            if (!area.isEmpty()) {
                                MybatisPlusConfig.myTableName.set("_" + area);
                            } else {
                                MybatisPlusConfig.myTableName.set("");
                            }
                            QueryWrapper<LinkM> querWrapperOut = new QueryWrapper<>();
                            querWrapperOut.eq("link_id", rdmsList.get(0).getManLinkid().toString());
                            relationSw2021q133.setOutlinkId(linkMMapper.selectOne(querWrapperOut).getHllLinkid());
                        }
                        // TYPE
                        switch (cdms.getCondType()) {
                            case 1:
                                relationSw2021q133.setType("3");
                                break;
                            case 4:
                                relationSw2021q133.setType("4");
                                break;
                            case 9:
                                relationSw2021q133.setType("5");
                                break;
                            case 16:
                                relationSw2021q133.setType("6");
                                break;
                            case 11:
                                relationSw2021q133.setType("8");
                                break;
                            default:
                        }

                        if (!country.isEmpty()) {
                            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
                        }
                        if (!area.isEmpty()) {
                            MybatisPlusConfig.myTableName.set("_" + area);
                        } else {
                            MybatisPlusConfig.myTableName.set("");
                        }
                        List<Cndmod> cndmodList = cndmodService.lambdaQuery().eq(Cndmod::getCondId, cdms.getCondId()).list();
                        String tollType = "";
                        char[] tollForm = {'0', '0', '0', '0', '0', '0', '0', '0'};
                        if (cndmodList.size() > 0) {
                            for (Cndmod cndmod : cndmodList
                            ) {
                                // TOLL_TYPE
                                if (cndmod.getModType() == 30) {
                                    switch (cndmod.getModVal()) {
                                        case "1":
                                            tollType += ",3";
                                            // relationSw2021q133.setTollType("3");
                                            break;
                                        case "2":
                                            tollType += ",2";
                                            // relationSw2021q133.setTollType("2");
                                            break;
                                        case "3":
                                            tollType += ",2";
                                            // relationSw2021q133.setTollType("2");
                                            break;
                                        case "4":
                                            tollType += ",20";
                                            // relationSw2021q133.setTollType("20");
                                            break;
                                        default:
                                    }
                                }
                                // TOLL_FORM
                                if (cndmod.getModType() == 31) {
                                    switch (cndmod.getModVal()) {
                                        case "1":
                                        case "7":
                                            tollForm[1] = '1';
                                            // relationSw2021q133.setTollForm("01000000");
                                            break;
                                        case "2":
                                            tollForm[2] = '1';
                                            // relationSw2021q133.setTollForm("00100000");
                                            break;
                                        case "3":
                                            tollForm[3] = '1';
                                            // relationSw2021q133.setTollForm("00010000");
                                            break;
                                        case "4":
                                        case "5":
                                        case "6":
                                            tollForm[0] = '1';
                                            // relationSw2021q133.setTollForm("10000000");
                                            break;
                                        case "8":
                                            tollForm[4] = '1';
                                            // relationSw2021q133.setTollForm("00001000");
                                            break;
                                        default:
                                    }
                                }
                            }
                            String tollTypeResult = tollType.replaceFirst(",", "");
                            relationSw2021q133.setTollType(tollTypeResult);
                            relationSw2021q133.setTollForm(new StringBuffer(String.valueOf(tollForm)).reverse().toString());
                        }

                        // VEH
                        char[] vehValue = {'0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0',
                                '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0'};
                        if ("Y".equals(cdms.getArAuto())) {
                            vehValue[0] = '1';
                        }
                        if ("Y".equals(cdms.getArTrucks())) {
                            vehValue[2] = '1';
                        }
                        if ("Y".equals(cdms.getArMotor())) {
                            vehValue[5] = '1';
                        }
                        if ("Y".equals(cdms.getArEmerveh())) {
                            vehValue[7] = '1';
                        }
                        if ("Y".equals(cdms.getArTaxis())) {
                            vehValue[8] = '1';
                        }
                        if ("Y".equals(cdms.getArBus())) {
                            vehValue[9] = '1';
                        }
                        if ("Y".equals(cdms.getArCarpool())) {
                            vehValue[13] = '1';
                        }
                        if ("Y".equals(cdms.getArDeliver())) {
                            vehValue[26] = '1';
                        }
                        relationSw2021q133.setVeh(String.valueOf(vehValue));

                        // GATE_TYPE
                        if (cdms.getCondType() == 4 && cdms.getCondVal1() != null) {
                            switch (cdms.getCondVal1()) {
                                case "EMERGENCY VEHICLE ACCESS":
                                    relationSw2021q133.setGateType("0");
                                    break;
                                case "KEY ACCESS":
                                    relationSw2021q133.setGateType("1");
                                    break;
                                case "PERMISSION REQUIRED":
                                    relationSw2021q133.setGateType("2");
                                    break;
                                default:
                            }
                        }
                        // TL_LOCAT
                        if (cdms.getCondType() == 11 || cdms.getCondType() == 16) {
                            if (cdms.getCondVal1() != null) {
                                char[] tlLocatVale = {'0', '0', '0'};
                                switch (cdms.getCondVal1()) {
                                    case "RIGHT":
                                        tlLocatVale[1] = '1';
                                        break;
                                    case "LEFT":
                                        tlLocatVale[0] = '1';
                                        break;
                                    case "OVERHEAD":
                                        tlLocatVale[2] = '1';
                                        break;
                                    default:
                                }
                                relationSw2021q133.setTlLocat(new StringBuffer(String.valueOf(tlLocatVale)).reverse().toString());
                            }
                        }
                        // DATASOURCE
                        relationSw2021q133.setDatasource("7");
                        // UP_DATE
                        relationSw2021q133.setUpDate(LocalDateTime.now());
                        // STATUS
                        relationSw2021q133.setStatus(0);
                        relationList.add(relationSw2021q133);
                    }
                }
            }

            log.info("relationList size is:" + relationList.size());
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            relationService.saveOrUpdateBatch(relationList);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("saveOrUpdate error,detail is {}", e.getMessage());
        } finally {
            countDownLatch.countDown();
        }
        // log.info("relation convert finished!");
        // relationMapper.mysqlInsertOrUpdateBath(relationList);
    }


    @Async("asyncTaskExecutor")
    public void ruleConvert(List<Cdms> cdmsList, Set<String> nodeIdSet, String area, String country, CountDownLatch countDownLatch) throws SQLException, InterruptedException {

        log.info("temp ds is:" + DynamicDataSourceContextHolder.peek());
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
        }

        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        final int areaAssign = StrUtil.isNotBlank(area)
                ? Integer.parseInt(area.replaceAll("\\D+", ""))
                : 0;


        List<RuleM> ruleList = new ArrayList<>();
        try {
            if (cdmsList.size() > 0) {
                for (Cdms cdms : cdmsList
                ) {
                    RuleM rule = new RuleM();
                    rule.setArea(areaAssign);
                    // RULE_ID
                    rule.setRuleId(UUID.randomUUID().toString());
                    if (cdms.getCondType() == 7||cdms.getCondType() == 39) {
                        // IN_LINKID
                        // 使用id继承，2022-01-20
                        // log.info("inLinkId:"+cdms.getLinkId());
                        // List<Long> inheritID1 = inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(cdms.getLinkId().toString())));
                        // rule.setInlinkId(inheritID1.get(0).toString());
                        if (!country.isEmpty()) {
                            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                        }

                        if (!area.isEmpty()) {
                            MybatisPlusConfig.myTableName.set("_" + area);
                        } else {
                            MybatisPlusConfig.myTableName.set("");
                        }
                        QueryWrapper<LinkM> linkQueryWrapper = new QueryWrapper<>();
                        linkQueryWrapper.eq("link_id", cdms.getLinkId().toString());
                        rule.setInlinkId(linkMMapper.selectOne(linkQueryWrapper).getHllLinkid());
                        // rule.setInlinkId(String.valueOf(cdms.getLinkId()));


                        if (!country.isEmpty()) {
                            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
                        }

                        if (!area.isEmpty()) {
                            MybatisPlusConfig.myTableName.set("_" + area);
                        } else {
                            MybatisPlusConfig.myTableName.set("");
                        }
                        log.info("temp herePhaStreetsService ds is:" + DynamicDataSourceContextHolder.peek());
                        List<HerePhaStreets> herePhaStreetsList = herePhaStreetsService.lambdaQuery().eq(HerePhaStreets::getLinkId, cdms.getLinkId())
                                .list();
                        // NODE_ID
                        // 使用id继承，2022-01-20
                        if (herePhaStreetsList.size() > 0) {
                            if ("N".equals(cdms.getEndOfLk())) {
                                // log.info("nodeId:"+herePhaStreetsList.get(0).getNrefInId());
                                // List<Long> inheritID = inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(herePhaStreetsList.get(0).getNrefInId().toString())));
                                // rule.setNodeId(inheritID.get(0).toString());
                                // rule.setNodeId(herePhaStreetsList.get(0).getNrefInId().toString());

                                if (!country.isEmpty()) {
                                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                                }

                                if (!area.isEmpty()) {
                                    MybatisPlusConfig.myTableName.set("_" + area);
                                } else {
                                    MybatisPlusConfig.myTableName.set("");
                                }
                                QueryWrapper<NodeM> nodeQueryWrapper = new QueryWrapper<>();
                                nodeQueryWrapper.eq("node_id", herePhaStreetsList.get(0).getNrefInId().toString());
                                rule.setNodeId(nodeMService.getOne(nodeQueryWrapper).getHllNodeid());

                            }
                            if ("R".equals(cdms.getEndOfLk())) {
                                // log.info("nodeId:"+herePhaStreetsList.get(0).getRefInId());
                                // List<Long> inheritID = inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(herePhaStreetsList.get(0).getRefInId().toString())));
                                // rule.setNodeId(inheritID.get(0).toString());
                                // rule.setNodeId(herePhaStreetsList.get(0).getRefInId().toString());

                                if (!country.isEmpty()) {
                                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                                }

                                if (!area.isEmpty()) {
                                    MybatisPlusConfig.myTableName.set("_" + area);
                                } else {
                                    MybatisPlusConfig.myTableName.set("");
                                }
                                QueryWrapper<NodeM> nodeQueryWrapper = new QueryWrapper<>();
                                nodeQueryWrapper.eq("node_id", herePhaStreetsList.get(0).getRefInId().toString());
                                rule.setNodeId(nodeMService.getOne(nodeQueryWrapper).getHllNodeid());

                            }
                            rule.setTileId(CommonUtils.getH3IndexByCentroid(nodeMService.getOne(Wrappers.<NodeM>lambdaQuery().eq(NodeM::getHllNodeid,rule.getNodeId())).getGeomwkt(),7));
                            rule.setTileType(Const.H3);
                        }
                        // OUT_LINKID
                        // 使用id继承，2022-01-20

                        if (!country.isEmpty()) {
                            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
                        }

                        if (!area.isEmpty()) {
                            MybatisPlusConfig.myTableName.set("_" + area);
                        } else {
                            MybatisPlusConfig.myTableName.set("");
                        }
                        List<Rdms> rdmsList = rdmsService.lambdaQuery().eq(Rdms::getCondId, cdms.getCondId())
                                .orderByAsc(Rdms::getSeqNumber).list();
                        if (rdmsList.size() > 0) {
                            // log.info("outLinkId:"+rdmsList.get(rdmsList.size() - 1).getManLinkid());
                            // List<Long> inheritID = inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(rdmsList.get(rdmsList.size() - 1).getManLinkid().toString())));
                            // rule.setOutlinkId(inheritID.get(0).toString());


                            if (!country.isEmpty()) {
                                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                            }

                            if (!area.isEmpty()) {
                                MybatisPlusConfig.myTableName.set("_" + area);
                            } else {
                                MybatisPlusConfig.myTableName.set("");
                            }
                            QueryWrapper<LinkM> outQueryMapper = new QueryWrapper<>();
                            outQueryMapper.eq("link_id", rdmsList.get(rdmsList.size() - 1).getManLinkid().toString());
                            rule.setOutlinkId(linkMMapper.selectOne(outQueryMapper).getHllLinkid());
                            // rule.setOutlinkId(rdmsList.get(rdmsList.size() - 1).getManLinkid().toString());

                            List<String> passLinkList = new ArrayList<>();
                            for (int i = 0; i < rdmsList.size() - 1; i++) {
                                passLinkList.add(rdmsList.get(i).getManLinkid().toString());
                            }
                            // PASS
                            if (passLinkList.size() == 0) {
                                rule.setPass(null);
                            } else {
                                // String passLinkString = String.join("|", passLinkList);
                                // rule.setPass(passLinkString);

                                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                                if (!country.isEmpty()) {
                                }

                                if (!area.isEmpty()) {
                                    MybatisPlusConfig.myTableName.set("_" + area);
                                } else {
                                    MybatisPlusConfig.myTableName.set("");
                                }
                                if (CollUtil.isNotEmpty(passLinkList)) {
                                    QueryWrapper<LinkM> passLinkQueryWrapper = new QueryWrapper<>();
                                    passLinkQueryWrapper.in("link_id", passLinkList);
                                    passLinkQueryWrapper.last("order by array_position(ARRAY" + passLinkList + "::VARCHAR[],link_id)");
                                    log.info("passLinkList is {}", passLinkList);
                                    List<String> hllLinkIds = linkMMapper.selectList(passLinkQueryWrapper).stream().map(LinkM::getHllLinkid).collect(Collectors.toList());
                                    rule.setPass(String.join("|", hllLinkIds));
                                } else {
                                    rule.setPass(null);
                                }
                                // if (passLinkString.length() > 256) {
                                //     String passString1 = passLinkString.substring(0, 257);
                                //     String pass = passString1.substring(0, passString1.lastIndexOf("|"));
                                //     String pass2 = passLinkString.substring(passString1.lastIndexOf("|") + 1);
                                //
                                //     rule.setPass(pass);
                                //     // if (StrUtil.isNotEmpty(pass)) {
                                //     //     if (pass.contains("|")) {
                                //     //         List<Long> inheritID2 = inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(pass.split("\\|"))));
                                //     //         String resPass = CollUtil.join(inheritID2, "|");
                                //     //         rule.setPass(resPass);
                                //     //     } else {
                                //     //         rule.setPass(String.valueOf(inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(pass))).get(0)));
                                //     //     }
                                //     // } else {
                                //     //     rule.setPass(pass);
                                //     // }
                                //
                                //     rule.setPass2(pass2);
                                //     // if (StrUtil.isNotEmpty(pass2)) {
                                //     //     if (pass2.contains("|")) {
                                //     //         List<Long> inheritID3 = inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(pass2.split("\\|"))));
                                //     //         String resPass2 = CollUtil.join(inheritID3, "|");
                                //     //         // rule.setPass2(resPass2);
                                //     //         rule.setPass(rule.getPass()+"|"+resPass2);
                                //     //     } else {
                                //     //         // rule.setPass2(String.valueOf(inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(pass2))).get(0)));
                                //     //         rule.setPass(rule.getPass()+"|"+ inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(pass2))).get(0));
                                //     //     }
                                //     // } else {
                                //     //     rule.setPass2(pass2);
                                //     // }
                                // } else {
                                //
                                //     rule.setPass2(passLinkString);
                                //     // if (StrUtil.isNotEmpty(passLinkString)) {
                                //     //     if (passLinkString.contains("|")) {
                                //     //         List<Long> inheritID4 = inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(passLinkString.split("\\|"))));
                                //     //         String resPass2 = CollUtil.join(inheritID4, "|");
                                //     //         rule.setPass2(resPass2);
                                //     //     } else {
                                //     //         rule.setPass2(String.valueOf(inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(passLinkString))).get(0)));
                                //     //     }
                                //     // } else {
                                //     //     rule.setPass2(passLinkString);
                                //     // }
                                //
                                //     // if (StrUtil.isNotEmpty(passLinkString)) {
                                //     //     rule.setPass(String.valueOf(inheritIDService.inheritID(new InheritIDDTO(12L, CollUtil.newArrayList(passLinkString))).get(0)));
                                //     // } else {
                                //     //     rule.setPass(passLinkString);
                                //     // }
                                // }
                            }
                        }
                        // FLAG
                        switch (cdms.getCondVal1()) {
                            case "LEGAL":
                            case "PHYSICAL":
                                rule.setFlag(1);
                                break;
                            case "LOGICAL":
                                rule.setFlag(2);
                                break;
                            case "OBSERVED":
                                rule.setFlag(0);
                                break;
                            default:
                                rule.setFlag(0);
                        }
                        if (cdms.getCondType()==39) {
                            rule.setFlag(3);
                        }
                        // VPERIOD
                        // log.info("temp cdmsdtmodService ds is:" + DynamicDataSourceContextHolder.peek());
                        if (!country.isEmpty()) {
                            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
                        }

                        if (!area.isEmpty()) {
                            MybatisPlusConfig.myTableName.set("_" + area);
                        } else {
                            MybatisPlusConfig.myTableName.set("");
                        }

                        List<Cdmsdtmod> cdmsdtmodList = cdmsdtmodService.lambdaQuery().eq(Cdmsdtmod::getCondId, cdms.getCondId()).list();
                        if (cdmsdtmodList.size() > 0) {
                            List<String> periodValueList = new ArrayList<>();

                            for (Cdmsdtmod cdmsdtmod : cdmsdtmodList
                            ) {
//                            if(periodValue.length()>0){
//                                periodValue += "｜";
//                            }
                                String dttmeType = cdmsdtmod.getDttmeType();
                                if ("A".equals(dttmeType)) {

                                    String periodValue = "";
                                    String timeValue = "", startTimeValue = "", endTimeValue = "";
                                    String dateValue = "", startDateValue = "", endDateValue = "";
                                    // startDateValue
                                    if (cdmsdtmod.getRefDate() != null) {
                                        if (CommonUtils.isNumeric(cdmsdtmod.getRefDate())) {
                                            startDateValue = "y" + cdmsdtmod.getRefDate().substring(0, 4)
                                                    + "M" + cdmsdtmod.getRefDate().substring(4, 6)
                                                    + "d" + cdmsdtmod.getRefDate().substring(6, 8);
                                        }
                                    }
                                    // endDateValue
                                    if (cdmsdtmod.getExpDate() != null) {
                                        if (CommonUtils.isNumeric(cdmsdtmod.getExpDate())) {
                                            endDateValue = "y" + cdmsdtmod.getExpDate().substring(0, 4)
                                                    + "M" + cdmsdtmod.getExpDate().substring(4, 6)
                                                    + "d" + cdmsdtmod.getExpDate().substring(6, 8);
                                        }
                                    }

                                    // startTimeValue
                                    if (cdmsdtmod.getStarttime() != null) {
                                        if (cdmsdtmod.getStarttime().length() > 0) {
                                            int hour = Integer.parseInt(cdmsdtmod.getStarttime().substring(0, 2));
                                            int minitue = Integer.parseInt(cdmsdtmod.getStarttime().substring(2, 4));
                                            if (minitue > 0) {
                                                startTimeValue += "h" + hour + "m" + minitue;
                                            } else {
                                                startTimeValue += "h" + hour;
                                            }
                                        }
                                    }

                                    // endTimeValue
                                    if (cdmsdtmod.getEndtime() != null) {
                                        if (cdmsdtmod.getEndtime().length() > 0) {
                                            int hour = Integer.parseInt(cdmsdtmod.getEndtime().substring(0, 2));
                                            int minitue = Integer.parseInt(cdmsdtmod.getEndtime().substring(2, 4));
                                            if (minitue > 0) {
                                                endTimeValue += "h" + hour + "m" + minitue;
                                            } else {
                                                endTimeValue += "h" + hour;
                                            }
                                        }
                                    }

                                    // combine time string
                                    if (startDateValue.length() > 0) {
                                        dateValue = "[(" + startDateValue + ")";
                                        if (endDateValue.length() > 0) {
                                            dateValue += "(" + endDateValue + ")]";
                                        } else {
                                            dateValue += "]";
                                        }
                                    } else {
                                        if (endDateValue.length() > 0) {
                                            dateValue = "[()(" + endDateValue + ")]";
                                        } else {
                                            dateValue = "";
                                        }
                                    }

//                            if(startDateValue.length()>0 && endDateValue !=null){
//                                dateValue = "[("+startDateValue+")("+endDateValue+")]";
//                            }
                                    if (startTimeValue.length() > 0) {
                                        timeValue = "[(" + startTimeValue + ")";
                                        if (endTimeValue.length() > 0) {
                                            timeValue += "(" + endTimeValue + ")]";
                                        } else {
                                            timeValue += "]";
                                        }
                                    } else {
                                        if (endTimeValue.length() > 0) {
                                            timeValue = "[()(" + endTimeValue + ")]";
                                        } else {
                                            timeValue = "";
                                        }
                                    }
//                            if(startTimeValue!=null && endTimeValue !=null){
//                                timeValue="[("+startTimeValue+")("+endTimeValue+")]";
//                            }

//                            if(startTvalue!=null && endTvalue !=null){
//                                tValue="("+startTvalue+")("+endTvalue+")";
//                            }
                                    if (dateValue.length() > 0) {
                                        periodValue = dateValue;
                                        if (timeValue.length() > 0) {
                                            periodValue += "*" + timeValue;
                                        } else {
                                            periodValue += "";
                                        }
                                    } else {
                                        if (timeValue.length() > 0) {
                                            periodValue = timeValue;
                                        } else {
                                            periodValue = "";
                                        }
                                    }
//                            if(dateValue.length()>0 && timeValue.length()>0){
//                                periodValue = dateValue+"*"+timeValue;
//                            }
//                            if(tValue.length()>0 && timeValue.length()>0 ){
//                                periodValue = timeValue+"*"+tValue;
//                            }
                                    log.info("periodValue:" + periodValue);
                                    periodValueList.add(periodValue);
                                }
                                if ("1".equals(dttmeType)) {
                                    String periodValue = "";
                                    String timeValue = "", startTimeValue = "", endTimeValue = "";
                                    String tValue = "", startTvalue = "", endTvalue = "";
                                    String dateValue = "", startDateValue = "", endDateValue = "";
                                    // startTvalue
                                    if (cdmsdtmod.getRefDate() != null) {
                                        if (cdmsdtmod.getRefDate().length() == 7) {
                                            if ("Y".equals(cdmsdtmod.getRefDate().split("")[0])) {
                                                startTvalue += "t1";
                                            }
                                            if ("Y".equals(cdmsdtmod.getRefDate().split("")[1])) {
                                                startTvalue += "t2";
                                            }
                                            if ("Y".equals(cdmsdtmod.getRefDate().split("")[2])) {
                                                startTvalue += "t3";
                                            }
                                            if ("Y".equals(cdmsdtmod.getRefDate().split("")[3])) {
                                                startTvalue += "t4";
                                            }
                                            if ("Y".equals(cdmsdtmod.getRefDate().split("")[4])) {
                                                startTvalue += "t5";
                                            }
                                            if ("Y".equals(cdmsdtmod.getRefDate().split("")[5])) {
                                                startTvalue += "t6";
                                            }
                                            if ("Y".equals(cdmsdtmod.getRefDate().split("")[6])) {
                                                startTvalue += "t7";
                                            }
                                        }
                                    }
                                    // endTvalue
                                    if (cdmsdtmod.getExpDate() != null) {
                                        if (cdmsdtmod.getExpDate().length() > 0 && cdmsdtmod.getExpDate().length() == 7) {
                                            if ("Y".equals(cdmsdtmod.getExpDate().split("")[0])) {
                                                endTvalue += "t1";
                                            }
                                            if ("Y".equals(cdmsdtmod.getExpDate().split("")[1])) {
                                                endTvalue += "t2";
                                            }
                                            if ("Y".equals(cdmsdtmod.getExpDate().split("")[2])) {
                                                endTvalue += "t3";
                                            }
                                            if ("Y".equals(cdmsdtmod.getExpDate().split("")[3])) {
                                                endTvalue += "t4";
                                            }
                                            if ("Y".equals(cdmsdtmod.getExpDate().split("")[4])) {
                                                endTvalue += "t5";
                                            }
                                            if ("Y".equals(cdmsdtmod.getExpDate().split("")[5])) {
                                                endTvalue += "t6";
                                            }
                                            if ("Y".equals(cdmsdtmod.getExpDate().split("")[6])) {
                                                endTvalue += "t7";
                                            }
                                        }
                                    }

                                    // startTimeValue
                                    if (cdmsdtmod.getStarttime() != null) {
                                        if (cdmsdtmod.getStarttime().length() > 0) {
                                            String hour = cdmsdtmod.getStarttime().substring(0, 2);
                                            String minitue = cdmsdtmod.getStarttime().substring(2, 4);
                                            if (Integer.parseInt(minitue) > 0) {
                                                startTimeValue += "h" + hour + "m" + minitue;
                                            } else {
                                                startTimeValue += "h" + hour;
                                            }
                                        }
                                    }

                                    // endTimeValue
                                    if (cdmsdtmod.getEndtime() != null) {
                                        if (cdmsdtmod.getEndtime().length() > 0) {
                                            String hour = cdmsdtmod.getEndtime().substring(0, 2);
                                            String minitue = cdmsdtmod.getEndtime().substring(2, 4);
                                            if (Integer.parseInt(minitue) > 0) {
                                                endTimeValue += "h" + hour + "m" + minitue;
                                            } else {
                                                endTimeValue += "h" + hour;
                                            }
                                        }
                                    }
                                    if (startTimeValue.length() > 0) {
                                        timeValue = "[(" + startTimeValue + ")";
                                        if (endTimeValue.length() > 0) {
                                            timeValue += "(" + endTimeValue + ")]";
                                        } else {
                                            timeValue += "]";
                                        }
                                    } else {
                                        if (endTimeValue.length() > 0) {
                                            timeValue = "[()(" + endTimeValue + ")]";
                                        } else {
                                            timeValue = "";
                                        }
                                    }

                                    if (startTvalue.length() > 0) {
                                        tValue = "(" + startTvalue + ")";
                                        if (endTvalue.length() > 0) {
                                            tValue += "(" + endTvalue + ")";
                                        } else {
                                            tValue += "";
                                        }
                                    } else {
                                        if (endTvalue.length() > 0) {
                                            tValue = "()(" + endTvalue + ")";
                                        } else {
                                            tValue = "";
                                        }
                                    }
                                    if (timeValue.length() > 0) {
                                        periodValue = timeValue;
                                        if (tValue.length() > 0) {
                                            periodValue += "*" + tValue;
                                        } else {
                                            periodValue += "";
                                        }
                                    } else {
                                        if (tValue.length() > 0) {
                                            periodValue = tValue;
                                        } else {
                                            periodValue = "";
                                        }
                                    }
                                    periodValueList.add(periodValue);
                                }

                                if ("C".equals(dttmeType)) {
                                    String periodValue = "";
                                    String timeValue = "", startTimeValue = "", endTimeValue = "";
                                    String dateValue = "", startDateValue = "", endDateValue = "";

                                    if (cdmsdtmod.getRefDate() != null) {
                                        //startDateValue
                                        startDateValue = "d" + cdmsdtmod.getRefDate().substring(2, 4);
                                        //endDateValue
                                        endDateValue = "d" + cdmsdtmod.getExpDate().substring(2, 4);

                                        // startTimeValue
                                        if (cdmsdtmod.getStarttime() != null) {
                                            if (cdmsdtmod.getStarttime().length() > 0) {
                                                String hour = cdmsdtmod.getStarttime().substring(0, 2);
                                                String minitue = cdmsdtmod.getStarttime().substring(2, 4);
                                                if (Integer.parseInt(minitue) > 0) {
                                                    startTimeValue += "h" + hour + "m" + minitue;
                                                } else {
                                                    startTimeValue += "h" + hour;
                                                }
                                            }
                                        }

                                        // endTimeValue
                                        if (cdmsdtmod.getEndtime() != null) {
                                            if (cdmsdtmod.getEndtime().length() > 0) {
                                                String hour = cdmsdtmod.getEndtime().substring(0, 2);
                                                String minitue = cdmsdtmod.getEndtime().substring(2, 4);
                                                if (Integer.parseInt(minitue) > 0) {
                                                    endTimeValue += "h" + hour + "m" + minitue;
                                                } else {
                                                    endTimeValue += "h" + hour;
                                                }
                                            }
                                        }

                                        if (startTimeValue.length() > 0) {
                                            timeValue = "[(" + startTimeValue + ")";
                                            if (endTimeValue.length() > 0) {
                                                timeValue += "(" + endTimeValue + ")]";
                                            } else {
                                                timeValue += "]";
                                            }
                                        } else {
                                            if (endTimeValue.length() > 0) {
                                                timeValue = "[()(" + endTimeValue + ")]";
                                            } else {
                                                timeValue = "";
                                            }
                                        }
                                        // combine time string
                                        if (startDateValue.length() > 0) {
                                            dateValue = "[(" + startDateValue + ")";
                                            if (endDateValue.length() > 0) {
                                                dateValue += "(" + endDateValue + ")]";
                                            } else {
                                                dateValue += "]";
                                            }
                                        } else {
                                            if (endDateValue.length() > 0) {
                                                dateValue = "[()(" + endDateValue + ")]";
                                            } else {
                                                dateValue = "";
                                            }
                                        }

                                        if (dateValue.length() > 0) {
                                            periodValue = dateValue;
                                            if (timeValue.length() > 0) {
                                                periodValue += "*" + timeValue;
                                            } else {
                                                periodValue += "";
                                            }
                                        } else {
                                            if (timeValue.length() > 0) {
                                                periodValue = timeValue;
                                            } else {
                                                periodValue = "";
                                            }
                                        }
                                    }
                                    periodValueList.add(periodValue);
                                }
                                if ("D".equals(dttmeType)) {
                                    String periodValue = "";
                                    String timeValue = "", startTimeValue = "", endTimeValue = "";
                                    String dateValue = "", startDateValue = "", endDateValue = "";

                                    if (cdmsdtmod.getRefDate() != null) {
                                        //startDateValue
                                        startDateValue = "w" + cdmsdtmod.getRefDate().substring(6, 8) + "d" + cdmsdtmod.getRefDate().substring(2, 4);
                                        //endDateValue
                                        endDateValue = "w" + cdmsdtmod.getRefDate().substring(6, 8) + "d" + cdmsdtmod.getExpDate().substring(2, 4);

                                        // startTimeValue
                                        if (cdmsdtmod.getStarttime() != null) {
                                            if (cdmsdtmod.getStarttime().length() > 0) {
                                                String hour = cdmsdtmod.getStarttime().substring(0, 2);
                                                String minitue = cdmsdtmod.getStarttime().substring(2, 4);
                                                if (Integer.parseInt(minitue) > 0) {
                                                    startTimeValue += "h" + hour + "m" + minitue;
                                                } else {
                                                    startTimeValue += "h" + hour;
                                                }
                                            }
                                        }

                                        // endTimeValue
                                        if (cdmsdtmod.getEndtime() != null) {
                                            if (cdmsdtmod.getEndtime().length() > 0) {
                                                String hour = cdmsdtmod.getEndtime().substring(0, 2);
                                                String minitue = cdmsdtmod.getEndtime().substring(2, 4);
                                                if (Integer.parseInt(minitue) > 0) {
                                                    endTimeValue += "h" + hour + "m" + minitue;
                                                } else {
                                                    endTimeValue += "h" + hour;
                                                }
                                            }
                                        }

                                        if (startTimeValue.length() > 0) {
                                            timeValue = "[(" + startTimeValue + ")";
                                            if (endTimeValue.length() > 0) {
                                                timeValue += "(" + endTimeValue + ")]";
                                            } else {
                                                timeValue += "]";
                                            }
                                        } else {
                                            if (endTimeValue.length() > 0) {
                                                timeValue = "[()(" + endTimeValue + ")]";
                                            } else {
                                                timeValue = "";
                                            }
                                        }
                                        // combine time string
                                        if (startDateValue.length() > 0) {
                                            dateValue = "[(" + startDateValue + ")";
                                            if (endDateValue.length() > 0) {
                                                dateValue += "(" + endDateValue + ")]";
                                            } else {
                                                dateValue += "]";
                                            }
                                        } else {
                                            if (endDateValue.length() > 0) {
                                                dateValue = "[()(" + endDateValue + ")]";
                                            } else {
                                                dateValue = "";
                                            }
                                        }

                                        if (dateValue.length() > 0) {
                                            periodValue = dateValue;
                                            if (timeValue.length() > 0) {
                                                periodValue += "*" + timeValue;
                                            } else {
                                                periodValue += "";
                                            }
                                        } else {
                                            if (timeValue.length() > 0) {
                                                periodValue = timeValue;
                                            } else {
                                                periodValue = "";
                                            }
                                        }
                                    }
                                    periodValueList.add(periodValue);
                                }
                                if ("E".equals(dttmeType)) {
                                    String periodValue = "";
                                    String timeValue = "", startTimeValue = "", endTimeValue = "";
                                    String dateValue = "", startDateValue = "", endDateValue = "";

                                    if (cdmsdtmod.getRefDate() != null) {
                                        //startDateValue
                                        startDateValue = "W" + cdmsdtmod.getRefDate().substring(6, 8) + "d" + cdmsdtmod.getRefDate().substring(2, 4);
                                        //endDateValue
                                        endDateValue = "W" + cdmsdtmod.getRefDate().substring(6, 8) + "d" + cdmsdtmod.getExpDate().substring(2, 4);

                                        // startTimeValue
                                        if (cdmsdtmod.getStarttime() != null) {
                                            if (cdmsdtmod.getStarttime().length() > 0) {
                                                String hour = cdmsdtmod.getStarttime().substring(0, 2);
                                                String minitue = cdmsdtmod.getStarttime().substring(2, 4);
                                                if (Integer.parseInt(minitue) > 0) {
                                                    startTimeValue += "h" + hour + "m" + minitue;
                                                } else {
                                                    startTimeValue += "h" + hour;
                                                }
                                            }
                                        }

                                        // endTimeValue
                                        if (cdmsdtmod.getEndtime() != null) {
                                            if (cdmsdtmod.getEndtime().length() > 0) {
                                                String hour = cdmsdtmod.getEndtime().substring(0, 2);
                                                String minitue = cdmsdtmod.getEndtime().substring(2, 4);
                                                if (Integer.parseInt(minitue) > 0) {
                                                    endTimeValue += "h" + hour + "m" + minitue;
                                                } else {
                                                    endTimeValue += "h" + hour;
                                                }
                                            }
                                        }

                                        if (startTimeValue.length() > 0) {
                                            timeValue = "[(" + startTimeValue + ")";
                                            if (endTimeValue.length() > 0) {
                                                timeValue += "(" + endTimeValue + ")]";
                                            } else {
                                                timeValue += "]";
                                            }
                                        } else {
                                            if (endTimeValue.length() > 0) {
                                                timeValue = "[()(" + endTimeValue + ")]";
                                            } else {
                                                timeValue = "";
                                            }
                                        }
                                        // combine time string
                                        if (startDateValue.length() > 0) {
                                            dateValue = "[(" + startDateValue + ")";
                                            if (endDateValue.length() > 0) {
                                                dateValue += "(" + endDateValue + ")]";
                                            } else {
                                                dateValue += "]";
                                            }
                                        } else {
                                            if (endDateValue.length() > 0) {
                                                dateValue = "[()(" + endDateValue + ")]";
                                            } else {
                                                dateValue = "";
                                            }
                                        }

                                        if (dateValue.length() > 0) {
                                            periodValue = dateValue;
                                            if (timeValue.length() > 0) {
                                                periodValue += "*" + timeValue;
                                            } else {
                                                periodValue += "";
                                            }
                                        } else {
                                            if (timeValue.length() > 0) {
                                                periodValue = timeValue;
                                            } else {
                                                periodValue = "";
                                            }
                                        }
                                    }
                                    periodValueList.add(periodValue);
                                }
                                if ("F".equals(dttmeType)) {
                                    String periodValue = "";
                                    String timeValue = "", startTimeValue = "", endTimeValue = "";
                                    String dateValue = "", startDateValue = "", endDateValue = "";

                                    if (cdmsdtmod.getRefDate() != null) {
                                        //startDateValue
                                        startDateValue = "w" + cdmsdtmod.getRefDate().substring(2, 4);
                                        //endDateValue
                                        endDateValue = "w" + cdmsdtmod.getExpDate().substring(2, 4);

                                        // startTimeValue
                                        if (cdmsdtmod.getStarttime() != null) {
                                            if (cdmsdtmod.getStarttime().length() > 0) {
                                                int hour = Integer.parseInt(cdmsdtmod.getStarttime().substring(0, 2));
                                                int minitue = Integer.parseInt(cdmsdtmod.getStarttime().substring(2, 4));
                                                if (minitue > 0) {
                                                    startTimeValue += "h" + hour + "m" + minitue;
                                                } else {
                                                    startTimeValue += "h" + hour;
                                                }
                                            }
                                        }

                                        // endTimeValue
                                        if (cdmsdtmod.getEndtime() != null) {
                                            if (cdmsdtmod.getEndtime().length() > 0) {
                                                String hour = cdmsdtmod.getEndtime().substring(0, 2);
                                                String minitue = cdmsdtmod.getEndtime().substring(2, 4);
                                                if (Integer.parseInt(minitue) > 0) {
                                                    endTimeValue += "h" + hour + "m" + minitue;
                                                } else {
                                                    endTimeValue += "h" + hour;
                                                }
                                            }
                                        }

                                        if (startTimeValue.length() > 0) {
                                            timeValue = "[(" + startTimeValue + ")";
                                            if (endTimeValue.length() > 0) {
                                                timeValue += "(" + endTimeValue + ")]";
                                            } else {
                                                timeValue += "]";
                                            }
                                        } else {
                                            if (endTimeValue.length() > 0) {
                                                timeValue = "[()(" + endTimeValue + ")]";
                                            } else {
                                                timeValue = "";
                                            }
                                        }
                                        // combine time string
                                        if (startDateValue.length() > 0) {
                                            dateValue = "[(" + startDateValue + ")";
                                            if (endDateValue.length() > 0) {
                                                dateValue += "(" + endDateValue + ")]";
                                            } else {
                                                dateValue += "]";
                                            }
                                        } else {
                                            if (endDateValue.length() > 0) {
                                                dateValue = "[()(" + endDateValue + ")]";
                                            } else {
                                                dateValue = "";
                                            }
                                        }

                                        if (dateValue.length() > 0) {
                                            periodValue = dateValue;
                                            if (timeValue.length() > 0) {
                                                periodValue += "*" + timeValue;
                                            } else {
                                                periodValue += "";
                                            }
                                        } else {
                                            if (timeValue.length() > 0) {
                                                periodValue = timeValue;
                                            } else {
                                                periodValue = "";
                                            }
                                        }
                                    }
                                    periodValueList.add(periodValue);
                                }
                                if ("H".equals(dttmeType)) {
                                    String periodValue = "";
                                    String timeValue = "", startTimeValue = "", endTimeValue = "";
                                    String dateValue = "", startDateValue = "", endDateValue = "";

                                    if (cdmsdtmod.getRefDate() != null) {
                                        //startDateValue
                                        startDateValue = "M" + cdmsdtmod.getRefDate().substring(2, 4);
                                        //endDateValue
                                        endDateValue = "M" + cdmsdtmod.getExpDate().substring(2, 4);

                                        // startTimeValue
                                        if (cdmsdtmod.getStarttime() != null) {
                                            if (cdmsdtmod.getStarttime().length() > 0) {
                                                String hour = cdmsdtmod.getStarttime().substring(0, 2);
                                                String minitue = cdmsdtmod.getStarttime().substring(2, 4);
                                                if (Integer.parseInt(minitue) > 0) {
                                                    startTimeValue += "h" + hour + "m" + minitue;
                                                } else {
                                                    startTimeValue += "h" + hour;
                                                }
                                            }
                                        }

                                        // endTimeValue
                                        if (cdmsdtmod.getEndtime() != null) {
                                            if (cdmsdtmod.getEndtime().length() > 0) {
                                                String hour = cdmsdtmod.getEndtime().substring(0, 2);
                                                String minitue = cdmsdtmod.getEndtime().substring(2, 4);
                                                if (Integer.parseInt(minitue) > 0) {
                                                    endTimeValue += "h" + hour + "m" + minitue;
                                                } else {
                                                    endTimeValue += "h" + hour;
                                                }
                                            }
                                        }

                                        if (startTimeValue.length() > 0) {
                                            timeValue = "[(" + startTimeValue + ")";
                                            if (endTimeValue.length() > 0) {
                                                timeValue += "(" + endTimeValue + ")]";
                                            } else {
                                                timeValue += "]";
                                            }
                                        } else {
                                            if (endTimeValue.length() > 0) {
                                                timeValue = "[()(" + endTimeValue + ")]";
                                            } else {
                                                timeValue = "";
                                            }
                                        }
                                        // combine time string
                                        if (startDateValue.length() > 0) {
                                            dateValue = "[(" + startDateValue + ")";
                                            if (endDateValue.length() > 0) {
                                                dateValue += "(" + endDateValue + ")]";
                                            } else {
                                                dateValue += "]";
                                            }
                                        } else {
                                            if (endDateValue.length() > 0) {
                                                dateValue = "[()(" + endDateValue + ")]";
                                            } else {
                                                dateValue = "";
                                            }
                                        }

                                        if (dateValue.length() > 0) {
                                            periodValue = dateValue;
                                            if (timeValue.length() > 0) {
                                                periodValue += "*" + timeValue;
                                            } else {
                                                periodValue += "";
                                            }
                                        } else {
                                            if (timeValue.length() > 0) {
                                                periodValue = timeValue;
                                            } else {
                                                periodValue = "";
                                            }
                                        }
                                    }
                                    periodValueList.add(periodValue);
                                }
                                if ("I".equals(dttmeType)) {
                                    String periodValue = "";
                                    String timeValue = "", startTimeValue = "", endTimeValue = "";
                                    String dateValue = "", startDateValue = "", endDateValue = "";

                                    if (cdmsdtmod.getRefDate() != null) {
                                        //startDateValue
                                        startDateValue = "M" + cdmsdtmod.getRefDate().substring(6, 8) + "d" + cdmsdtmod.getRefDate().substring(2, 4);
                                        //endDateValue
                                        endDateValue = "M" + cdmsdtmod.getRefDate().substring(6, 8) + "d" + cdmsdtmod.getExpDate().substring(2, 4);

                                        // startTimeValue
                                        if (cdmsdtmod.getStarttime() != null) {
                                            if (cdmsdtmod.getStarttime().length() > 0) {
                                                String hour = cdmsdtmod.getStarttime().substring(0, 2);
                                                String minitue = cdmsdtmod.getStarttime().substring(2, 4);
                                                if (Integer.parseInt(minitue) > 0) {
                                                    startTimeValue += "h" + hour + "m" + minitue;
                                                } else {
                                                    startTimeValue += "h" + hour;
                                                }
                                            }
                                        }

                                        // endTimeValue
                                        if (cdmsdtmod.getEndtime() != null) {
                                            if (cdmsdtmod.getEndtime().length() > 0) {
                                                String hour = cdmsdtmod.getEndtime().substring(0, 2);
                                                String minitue = cdmsdtmod.getEndtime().substring(2, 4);
                                                if (Integer.parseInt(minitue) > 0) {
                                                    endTimeValue += "h" + hour + "m" + minitue;
                                                } else {
                                                    endTimeValue += "h" + hour;
                                                }
                                            }
                                        }

                                        if (startTimeValue.length() > 0) {
                                            timeValue = "[(" + startTimeValue + ")";
                                            if (endTimeValue.length() > 0) {
                                                timeValue += "(" + endTimeValue + ")]";
                                            } else {
                                                timeValue += "]";
                                            }
                                        } else {
                                            if (endTimeValue.length() > 0) {
                                                timeValue = "[()(" + endTimeValue + ")]";
                                            } else {
                                                timeValue = "";
                                            }
                                        }
                                        // combine time string
                                        if (startDateValue.length() > 0) {
                                            dateValue = "[(" + startDateValue + ")";
                                            if (endDateValue.length() > 0) {
                                                dateValue += "(" + endDateValue + ")]";
                                            } else {
                                                dateValue += "]";
                                            }
                                        } else {
                                            if (endDateValue.length() > 0) {
                                                dateValue = "[()(" + endDateValue + ")]";
                                            } else {
                                                dateValue = "";
                                            }
                                        }

                                        if (dateValue.length() > 0) {
                                            periodValue = dateValue;
                                            if (timeValue.length() > 0) {
                                                periodValue += "*" + timeValue;
                                            } else {
                                                periodValue += "";
                                            }
                                        } else {
                                            if (timeValue.length() > 0) {
                                                periodValue = timeValue;
                                            } else {
                                                periodValue = "";
                                            }
                                        }
                                    }
                                    periodValueList.add(periodValue);
                                }
                            }

                            log.info("periodValueResult is:" + String.join("|", periodValueList));
                            rule.setVperiod(String.join("|", periodValueList));
                        }
                        // VEH
                        char[] vehValue = {'0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0',
                                '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0'};
                        if ("Y".equals(cdms.getArAuto())) {
                            vehValue[0] = '1';
                        }
                        if ("Y".equals(cdms.getArTrucks())) {
                            vehValue[2] = '1';
                        }
                        if ("Y".equals(cdms.getArMotor())) {
                            vehValue[5] = '1';
                        }
                        if ("Y".equals(cdms.getArEmerveh())) {
                            vehValue[7] = '1';
                        }
                        if ("Y".equals(cdms.getArTaxis())) {
                            vehValue[8] = '1';
                        }
                        if ("Y".equals(cdms.getArBus())) {
                            vehValue[9] = '1';
                        }
                        if ("Y".equals(cdms.getArCarpool())) {
                            vehValue[13] = '1';
                        }
                        if ("Y".equals(cdms.getArDeliver())) {
                            vehValue[26] = '1';
                        }
                        rule.setVehclType(String.valueOf(vehValue));
                        // UP_DATE
                        rule.setUpDate(LocalDateTime.now());
                        // DATASOURCE
                        rule.setDatasource("7");
                        // STATUS
                        rule.setStatus(0);
                        ruleList.add(rule);
                    }
                }
                log.info("ruleList size is:" + ruleList.size());
                if (!country.isEmpty()) {
                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                }

                if (!area.isEmpty()) {
                    MybatisPlusConfig.myTableName.set("_" + area);
                } else {
                    MybatisPlusConfig.myTableName.set("");
                }
                List<Long> ids = inheritIDService.createID(new InheritIDDTO(12L, Long.valueOf(ruleList.size())));
                for (int i = 0; i < ruleList.size(); i++) {
                    ruleList.get(i).setRuleId(ids.get(i).toString());
                }
                // List<String> srcLinkIds = ruleList.stream().map(Poi::getLinkId).collect(Collectors.toList());
                // List<Long> linkIds = inheritIDService.inheritID(new InheritIDDTO(12L, srcLinkIds));
                // if (srcLinkIds.size() == linkIds.size()) {
                //     for (int j = 0; j < pois.size(); j++) {
                //         pois.get(j).setLinkId(linkIds.get(j).toString());
                //     }
                // }


                // List<String> inlinkIds = ruleList.stream().map(RuleM::getInlinkId).collect(Collectors.toList());
                // List<Long> inLinkIdsRes = inheritIDService.inheritID(new InheritIDDTO(12L, inlinkIds));
                // List<String> nodeIds = ruleList.stream().map(RuleM::getNodeId).collect(Collectors.toList());
                // List<Long> nodeIdsRes = inheritIDService.inheritID(new InheritIDDTO(12L, nodeIds));
                // List<String> outlinkIds = ruleList.stream().map(RuleM::getOutlinkId).collect(Collectors.toList());
                // List<Long> outlinkIdsRes = inheritIDService.inheritID(new InheritIDDTO(12L, outlinkIds));
                // for (int i = 0; i < ruleList.size(); i++) {
                //     ruleList.get(i).setInlinkId(String.valueOf(inLinkIdsRes.get(i)));
                //     ruleList.get(i).setNodeId(String.valueOf(nodeIdsRes.get(i)));
                //     ruleList.get(i).setOutlinkId(String.valueOf(outlinkIdsRes.get(i)));
                // }

                // log.info("temp ruleService ds is:" + DynamicDataSourceContextHolder.peek());
                // ruleService.saveOrUpdateBatch(ruleList);
                // log.info("rule convert finished!");
                int step = 32767 / BeanUtil.beanToMap(new RuleM()).keySet().size();
                List<List<RuleM>> partition = Lists.partition(ruleList, step);
                for (List<RuleM> ruleMS : partition) {
                    ruleMapper.mysqlInsertOrUpdateBath(ruleMS);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            countDownLatch.countDown();
        }

    }

    public static void main(String args[]) {
        String[] passLinkList = {"123456999999999909090909090789", "456789082838274264287647826420102010313",
                "789012308934723897429401023123", "0123029340284029842045678909201931", "12301233312212384728472",
                "4560000", "7890000", "0123126378273238", "123456999999999909090909090789", "456789082838274264287647826420102010313",
                "789012308934723897429401023123", "0123029340284029842045678909201931", "12301233312212384728472",
                "4560000", "7890000", "0123126378273238"};
        String passLinkString = String.join("|", passLinkList);
        if (passLinkString.length() > 256) {
            String passString1 = passLinkString.substring(0, 257);
            String pass = passString1.substring(0, passString1.lastIndexOf("|"));
            String pass2 = passLinkString.substring(passString1.lastIndexOf("|") + 1);
            log.info(pass);
        }
    }

    @Async("asyncTaskExecutor")
    public void releationConvert2(CountDownLatch countDownLatch, List<Cdms> cdmsList, String area, String country) {

        List<RelationM> relationList = new ArrayList<>();
        try {
            if (cdmsList.size() > 0) {
                for (Cdms cdms : cdmsList
                ) {

                    if (cdms.getCondType() == 1 || cdms.getCondType() == 4 || cdms.getCondType() == 9
                            || cdms.getCondType() == 11 || cdms.getCondType() == 16) {
                        RelationM relation = new RelationM();
                        // RELATIONID 暂时不赋值,调用id服务来获取
                        // relationSw2021q133.setRelationid(UUID.randomUUID().toString());
                        if (!country.isEmpty()) {
                            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                        }
                        if (!area.isEmpty()) {
                            MybatisPlusConfig.myTableName.set("_" + area);
                        } else {
                            MybatisPlusConfig.myTableName.set("");
                        }
                        QueryWrapper<LinkM> queryWrapperIn = new QueryWrapper<>();
                        queryWrapperIn.eq("link_id", cdms.getLinkId().toString());
                        relation.setInlinkId(linkMMapper.selectOne(queryWrapperIn).getHllLinkid());


                        if (!country.isEmpty()) {
                            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
                        }
                        if (!area.isEmpty()) {
                            MybatisPlusConfig.myTableName.set("_" + area);
                        } else {
                            MybatisPlusConfig.myTableName.set("");
                        }
                        List<HerePhaStreets> herePhaStreetsList = herePhaStreetsService.lambdaQuery().eq(HerePhaStreets::getLinkId, cdms.getLinkId())
                                .list();
                        // NODEID
                        // 使用id继承，2022-01-20
                        if (herePhaStreetsList.size() > 0) {
                            if ("N".equals(cdms.getEndOfLk())) {
                                if (!country.isEmpty()) {
                                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                                }
                                if (!area.isEmpty()) {
                                    MybatisPlusConfig.myTableName.set("_" + area);
                                } else {
                                    MybatisPlusConfig.myTableName.set("");
                                }

                                QueryWrapper<NodeM> querWrapperNode = new QueryWrapper<>();
                                querWrapperNode.eq("node_id", herePhaStreetsList.get(0).getNrefInId().toString());
                                relation.setNodeId(nodeMService.getOne(querWrapperNode).getHllNodeid());
                            }
                            if ("R".equals(cdms.getEndOfLk())) {
                                if (!country.isEmpty()) {
                                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                                }
                                if (!area.isEmpty()) {
                                    MybatisPlusConfig.myTableName.set("_" + area);
                                } else {
                                    MybatisPlusConfig.myTableName.set("");
                                }
                                QueryWrapper<NodeM> querWrapperNode = new QueryWrapper<>();
                                querWrapperNode.eq("node_id", herePhaStreetsList.get(0).getRefInId().toString());
                                relation.setNodeId(nodeMService.getOne(querWrapperNode).getHllNodeid());
                            }

                            relation.setTileId(CommonUtils.getH3IndexByCentroid(nodeMService.getOne(Wrappers.<NodeM>lambdaQuery().eq(NodeM::getHllNodeid,relation.getNodeId())).getGeomwkt(),7));
                            relation.setTileType(Const.H3);
                            // PASS_NUM
                            relation.setPassNum(herePhaStreetsList.get(0).getPhysLanes().toString());
                        }

                        if (!country.isEmpty()) {
                            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
                        }
                        if (!area.isEmpty()) {
                            MybatisPlusConfig.myTableName.set("_" + area);
                        } else {
                            MybatisPlusConfig.myTableName.set("");
                        }
                        List<Rdms> rdmsList = rdmsService.lambdaQuery().eq(Rdms::getCondId, cdms.getCondId()).list();
                        if (rdmsList.size() > 0) {
                            if (!country.isEmpty()) {
                                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                            }
                            if (!area.isEmpty()) {
                                MybatisPlusConfig.myTableName.set("_" + area);
                            } else {
                                MybatisPlusConfig.myTableName.set("");
                            }
                            QueryWrapper<LinkM> querWrapperOut = new QueryWrapper<>();
                            querWrapperOut.eq("link_id", rdmsList.get(0).getManLinkid().toString());
                            relation.setOutlinkId(linkMMapper.selectOne(querWrapperOut).getHllLinkid());
//                            log.info("进入LinkId:{},进入原始LinkId:{},退出LinkId:{},退出原始LinkId:{}", relationSw2021q133.getInlinkId(),cdms.getLinkId(), relationSw2021q133.getOutlinkId(), rdmsList.get(0).getManLinkid().toString());
                        }
                        // TYPE
                        switch (cdms.getCondType()) {
                            case 1:
                                relation.setType("3");
                                break;
                            case 4:
                                relation.setType("4");
                                break;
                            case 9:
                                relation.setType("5");
                                break;
                            case 16:
                                relation.setType("6");
                                break;
                            case 11:
                                relation.setType("8");
                                break;
                            default:
                        }

                        if (!country.isEmpty()) {
                            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
                        }
                        if (!area.isEmpty()) {
                            MybatisPlusConfig.myTableName.set("_" + area);
                        } else {
                            MybatisPlusConfig.myTableName.set("");
                        }
                        List<Cndmod> cndmodList = cndmodService.lambdaQuery().eq(Cndmod::getCondId, cdms.getCondId()).list();
                        String tollType = "";
                        char[] tollForm = {'0', '0', '0', '0', '0', '0', '0', '0'};
                        if (cndmodList.size() > 0) {
                            for (Cndmod cndmod : cndmodList
                            ) {
                                // TOLL_TYPE
                                if (cndmod.getModType() == 30) {
                                    switch (cndmod.getModVal()) {
                                        case "1":
                                            tollType += ",3";
                                            // relationSw2021q133.setTollType("3");
                                            break;
                                        case "2":
                                            tollType += ",2";
                                            // relationSw2021q133.setTollType("2");
                                            break;
                                        case "3":
                                            tollType += ",2";
                                            // relationSw2021q133.setTollType("2");
                                            break;
                                        case "4":
                                            tollType += ",20";
                                            // relationSw2021q133.setTollType("20");
                                            break;
                                        default:
                                    }
                                }
                                // TOLL_FORM
                                if (cndmod.getModType() == 31) {
                                    switch (cndmod.getModVal()) {
                                        case "1":
                                        case "7":
                                            tollForm[1] = '1';
                                            // relationSw2021q133.setTollForm("01000000");
                                            break;
                                        case "2":
                                            tollForm[2] = '1';
                                            // relationSw2021q133.setTollForm("00100000");
                                            break;
                                        case "3":
                                            tollForm[3] = '1';
                                            // relationSw2021q133.setTollForm("00010000");
                                            break;
                                        case "4":
                                        case "5":
                                        case "6":
                                            tollForm[0] = '1';
                                            // relationSw2021q133.setTollForm("10000000");
                                            break;
                                        case "8":
                                            tollForm[4] = '1';
                                            // relationSw2021q133.setTollForm("00001000");
                                            break;
                                        default:
                                    }
                                }
                            }
                            String tollTypeResult = tollType.replaceFirst(",", "");
                            relation.setTollType(tollTypeResult);
                            relation.setTollForm(new StringBuffer(String.valueOf(tollForm)).reverse().toString());
                        }

                        // VEH
                        char[] vehValue = {'0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0',
                                '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0'};
                        if ("Y".equals(cdms.getArAuto())) {
                            vehValue[0] = '1';
                        }
                        if ("Y".equals(cdms.getArTrucks())) {
                            vehValue[2] = '1';
                        }
                        if ("Y".equals(cdms.getArMotor())) {
                            vehValue[5] = '1';
                        }
                        if ("Y".equals(cdms.getArEmerveh())) {
                            vehValue[7] = '1';
                        }
                        if ("Y".equals(cdms.getArTaxis())) {
                            vehValue[8] = '1';
                        }
                        if ("Y".equals(cdms.getArBus())) {
                            vehValue[9] = '1';
                        }
                        if ("Y".equals(cdms.getArCarpool())) {
                            vehValue[13] = '1';
                        }
                        if ("Y".equals(cdms.getArDeliver())) {
                            vehValue[26] = '1';
                        }
                        relation.setVeh(String.valueOf(vehValue));

                        // GATE_TYPE
                        if (cdms.getCondType() == 4 && cdms.getCondVal1() != null) {
                            switch (cdms.getCondVal1()) {
                                case "EMERGENCY VEHICLE ACCESS":
                                    relation.setGateType("0");
                                    break;
                                case "KEY ACCESS":
                                    relation.setGateType("1");
                                    break;
                                case "PERMISSION REQUIRED":
                                    relation.setGateType("2");
                                    break;
                                default:
                            }
                        }
                        // TL_LOCAT
                        if (cdms.getCondType() == 11 || cdms.getCondType() == 16) {
                            if (cdms.getCondVal1() != null) {
                                char[] tlLocatVale = {'0', '0', '0'};
                                switch (cdms.getCondVal1()) {
                                    case "RIGHT":
                                        tlLocatVale[1] = '1';
                                        break;
                                    case "LEFT":
                                        tlLocatVale[0] = '1';
                                        break;
                                    case "OVERHEAD":
                                        tlLocatVale[2] = '1';
                                        break;
                                    default:
                                }
                                relation.setTlLocat(new StringBuffer(String.valueOf(tlLocatVale)).reverse().toString());
                            }
                        }
                        // DATASOURCE
                        relation.setDatasource("7");
                        // UP_DATE
                        relation.setUpDate(LocalDateTime.now());
                        // STATUS
                        relation.setStatus(0);
                        final int areaAssign = StrUtil.isNotBlank(area)
                                ? Integer.parseInt(area.replaceAll("\\D+", ""))
                                : 0;
                        relation.setArea(areaAssign);
                        relationList.add(relation);
                    }
                }
            }
            List<Long> ids = inheritIDService.createID(new InheritIDDTO(12L, Long.valueOf(relationList.size())));
            for (int i = 0; i < relationList.size(); i++) {
                relationList.get(i).setRelationid(ids.get(i).toString());
            }

            log.info("relationList size is:" + relationList.size());
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
//            relationService.saveOrUpdateBatch(relationList);
            relationMapper.mysqlInsertOrUpdateBath(relationList);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("saveOrUpdate error,detail is {}", e.getMessage());
        } finally {
            countDownLatch.countDown();
        }
    }

    /**
     * Optimized version of releationConvert2 with better memory management and performance
     * Uses optimized thread pool and proper resource cleanup
     */
    @Async("optimizedAsyncTaskExecutor")
    public CompletableFuture<Void> releationConvertOptimized(CountDownLatch countDownLatch,
                                                            List<Cdms> cdmsList,
                                                            String area,
                                                            String country) {
        try {
            // Log memory usage at start
            logMemoryUsage("Start of releationConvertOptimized");

            // Set database context
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }

            // Process relations with optimized logic
            List<RelationM> relationList = processRelationsOptimized(cdmsList, area, country);

            // Save relations in batches for better performance
            if (!relationList.isEmpty()) {
                saveRelationsInBatches(relationList, area, country);
            }

            log.info("Optimized relation conversion completed for {} CDMS records, generated {} relations",
                    cdmsList.size(), relationList.size());

            // Log memory usage after processing
            logMemoryUsage("After processing relations");

        } catch (Exception e) {
            log.error("Error in optimized relation convert", e);
        } finally {
            // Cleanup resources
            cleanupResources();

            // Log final memory usage
            logMemoryUsage("End of releationConvertOptimized");

            // Always count down the latch to prevent deadlocks
            countDownLatch.countDown();
        }

        return CompletableFuture.completedFuture(null);
    }

    /**
     * Process relations with optimized logic and memory management
     */
    private List<RelationM> processRelationsOptimized(List<Cdms> cdmsList, String area, String country) {
        List<RelationM> relationList = new ArrayList<>();

        // Pre-load configuration data to avoid repeated database queries
        Map<Integer, List<Rdms>> rdmsCache = preloadRdmsData(cdmsList);
        Map<Integer, List<HerePhaStreets>> streetsCache = preloadStreetsData(cdmsList, area, country);
        Map<Integer, List<Cndmod>> cndmodCache = preloadCndmodData(cdmsList);

        log.info("Pre-loaded cache data: {} RDMS entries, {} Streets entries, {} Cndmod entries",
                rdmsCache.size(), streetsCache.size(), cndmodCache.size());

        try {
            for (Cdms cdms : cdmsList) {
                // Only process specific condition types
                if (cdms.getCondType() == 1 || cdms.getCondType() == 4 || cdms.getCondType() == 9
                        || cdms.getCondType() == 11 || cdms.getCondType() == 16) {

                    RelationM relation = createOptimizedRelation(cdms, area, country,
                            rdmsCache, streetsCache, cndmodCache);

                    if (relation != null) {
                        relationList.add(relation);
                    }
                }
            }

            // Batch generate IDs for all relations at once
            if (!relationList.isEmpty()) {
                generateRelationIds(relationList);
            }

        } catch (Exception e) {
            log.error("Error processing relations", e);
            throw new RuntimeException("Failed to process relations", e);
        } finally {
            // Clear caches to free memory
            rdmsCache.clear();
            streetsCache.clear();
            cndmodCache.clear();
        }

        return relationList;
    }

    /**
     * Pre-load RDMS data to avoid repeated database queries
     */
    private Map<Integer, List<Rdms>> preloadRdmsData(List<Cdms> cdmsList) {
        Map<Integer, List<Rdms>> rdmsCache = new HashMap<>();

        // Extract unique condition IDs
        Set<Integer> condIds = cdmsList.stream()
                .map(Cdms::getCondId)
                .collect(Collectors.toSet());

        if (!condIds.isEmpty()) {
            // Batch query all RDMS data
            List<Rdms> allRdms = rdmsService.lambdaQuery()
                    .in(Rdms::getCondId, condIds)
                    .list();

            // Group by condition ID for fast lookup
            rdmsCache = allRdms.stream()
                    .collect(Collectors.groupingBy(Rdms::getCondId));
        }

        return rdmsCache;
    }

    /**
     * Pre-load Streets data to avoid repeated database queries
     */
    private Map<Integer, List<HerePhaStreets>> preloadStreetsData(List<Cdms> cdmsList, String area, String country) {
        Map<Integer, List<HerePhaStreets>> streetsCache = new HashMap<>();

        // Set database context for streets query
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, true));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }

        // Extract unique link IDs
        Set<Integer> linkIds = cdmsList.stream()
                .map(Cdms::getLinkId)
                .collect(Collectors.toSet());

        if (!linkIds.isEmpty()) {
            // Batch query all Streets data
            List<HerePhaStreets> allStreets = herePhaStreetsService.lambdaQuery()
                    .in(HerePhaStreets::getLinkId, linkIds)
                    .list();

            // Group by link ID for fast lookup
            streetsCache = allStreets.stream()
                    .collect(Collectors.groupingBy(HerePhaStreets::getLinkId));
        }

        return streetsCache;
    }

    /**
     * Pre-load Cndmod data to avoid repeated database queries
     */
    private Map<Integer, List<Cndmod>> preloadCndmodData(List<Cdms> cdmsList) {
        Map<Integer, List<Cndmod>> cndmodCache = new HashMap<>();

        // Extract unique condition IDs
        Set<Integer> condIds = cdmsList.stream()
                .map(Cdms::getCondId)
                .collect(Collectors.toSet());

        if (!condIds.isEmpty()) {
            // Batch query all Cndmod data
            List<Cndmod> allCndmod = cndmodService.lambdaQuery()
                    .in(Cndmod::getCondId, condIds)
                    .list();

            // Group by condition ID for fast lookup
            cndmodCache = allCndmod.stream()
                    .collect(Collectors.groupingBy(Cndmod::getCondId));
        }

        return cndmodCache;
    }

    /**
     * Create optimized relation using pre-loaded cache data
     */
    private RelationM createOptimizedRelation(Cdms cdms, String area, String country,
                                            Map<Integer, List<Rdms>> rdmsCache,
                                            Map<Integer, List<HerePhaStreets>> streetsCache,
                                            Map<Integer, List<Cndmod>> cndmodCache) {
        try {
            RelationM relation = new RelationM();

            // Set InLink ID using cached streets data
            List<HerePhaStreets> herePhaStreetsList = streetsCache.get(cdms.getLinkId());
            if (herePhaStreetsList == null || herePhaStreetsList.isEmpty()) {
                log.warn("No streets data found for link ID: {}", cdms.getLinkId());
                return null;
            }

            // Set database context for link queries
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }

            // Set InLink ID
            QueryWrapper<LinkM> querWrapperIn = new QueryWrapper<>();
            querWrapperIn.eq("link_id", cdms.getLinkId().toString());
            LinkM inLink = linkMMapper.selectOne(querWrapperIn);
            if (inLink != null) {
                relation.setInlinkId(inLink.getHllLinkid());
            } else {
                log.warn("No link found for link ID: {}", cdms.getLinkId());
                return null;
            }

            // Set Node ID based on end of link
            if ("N".equals(cdms.getEndOfLk())) {
                QueryWrapper<NodeM> querWrapperNode = new QueryWrapper<>();
                querWrapperNode.eq("node_id", herePhaStreetsList.get(0).getNrefInId().toString());
                NodeM node = nodeMService.getOne(querWrapperNode);
                if (node != null) {
                    relation.setNodeId(node.getHllNodeid());
                }
            } else if ("R".equals(cdms.getEndOfLk())) {
                QueryWrapper<NodeM> querWrapperNode = new QueryWrapper<>();
                querWrapperNode.eq("node_id", herePhaStreetsList.get(0).getRefInId().toString());
                NodeM node = nodeMService.getOne(querWrapperNode);
                if (node != null) {
                    relation.setNodeId(node.getHllNodeid());
                }
            }

            // Set PASS_NUM from HerePhaStreets physLanes
            relation.setPassNum(herePhaStreetsList.get(0).getPhysLanes().toString());

            // Set OutLink ID using cached RDMS data
            List<Rdms> rdmsList = rdmsCache.get(cdms.getCondId());
            if (rdmsList != null && !rdmsList.isEmpty()) {
                QueryWrapper<LinkM> querWrapperOut = new QueryWrapper<>();
                querWrapperOut.eq("link_id", rdmsList.get(0).getManLinkid().toString());
                LinkM outLink = linkMMapper.selectOne(querWrapperOut);
                if (outLink != null) {
                    relation.setOutlinkId(outLink.getHllLinkid());
                }
            }

            // Set relation type based on condition type
            setRelationType(relation, cdms.getCondType());

            // Process toll information using cached Cndmod data
            processTollInformation(relation, cdms.getCondId(), cndmodCache);

            // Process vehicle restrictions (VEH field)
            processVehicleRestrictions(relation, cdms);

            // Process gate type logic for condition type 4
            processGateType(relation, cdms);

            // Process traffic light location for condition types 11 and 16
            processTrafficLightLocation(relation, cdms);

            // Set common fields
            relation.setDatasource("7");
            relation.setUpDate(LocalDateTime.now());
            relation.setStatus(0);
            final int areaAssign = StrUtil.isNotBlank(area)
                    ? Integer.parseInt(area.replaceAll("\\D+", ""))
                    : 0;
            relation.setArea(areaAssign);


            return relation;

        } catch (Exception e) {
            log.error("Error creating relation for CDMS ID: {}", cdms.getCondId(), e);
            return null;
        }
    }

    /**
     * Set relation type based on condition type
     */
    private void setRelationType(RelationM relation, Integer condType) {
        switch (condType) {
            case 1:
                relation.setType("3");
                break;
            case 4:
                relation.setType("4");
                break;
            case 9:
                relation.setType("5");
                break;
            case 16:
                relation.setType("6");
                break;
            case 11:
                relation.setType("8");
                break;
            default:
                log.warn("Unknown condition type: {}", condType);
        }
    }

    /**
     * Process toll information using cached Cndmod data
     */
    private void processTollInformation(RelationM relation, Integer condId,
                                      Map<Integer, List<Cndmod>> cndmodCache) {
        List<Cndmod> cndmodList = cndmodCache.get(condId);
        if (cndmodList == null || cndmodList.isEmpty()) {
            return;
        }

        String tollType = "";
        char[] tollForm = {'0', '0', '0', '0', '0', '0', '0', '0'};

        for (Cndmod cndmod : cndmodList) {
            // TOLL_TYPE - match original logic exactly
            if (cndmod.getModType() == 30) {
                switch (cndmod.getModVal()) {
                    case "1":
                        tollType += ",3";
                        break;
                    case "2":
                        tollType += ",2";
                        break;
                    case "3":
                        tollType += ",2";
                        break;
                    case "4":
                        tollType += ",20";
                        break;
                    default:
                        break;
                }
            }

            // TOLL_FORM
            if (cndmod.getModType() == 31) {
                switch (cndmod.getModVal()) {
                    case "1":
                    case "7":
                        tollForm[1] = '1';
                        break;
                    case "2":
                        tollForm[2] = '1';
                        break;
                    case "3":
                        tollForm[3] = '1';
                        break;
                    case "4":
                    case "5":
                    case "6":
                        tollForm[0] = '1';
                        break;
                    case "8":
                        tollForm[4] = '1';
                        break;
                    default:
                        break;
                }
            }
        }

        String tollTypeResult = tollType.replaceFirst(",", "");
        relation.setTollType(tollTypeResult);
        relation.setTollForm(new StringBuffer(String.valueOf(tollForm)).reverse().toString());
    }

    /**
     * Process vehicle restrictions (VEH field) - matches original logic exactly
     */
    private void processVehicleRestrictions(RelationM relation, Cdms cdms) {
        char[] vehValue = {'0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0',
                '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0'};

        if ("Y".equals(cdms.getArAuto())) {
            vehValue[0] = '1';
        }
        if ("Y".equals(cdms.getArTrucks())) {
            vehValue[2] = '1';
        }
        if ("Y".equals(cdms.getArMotor())) {
            vehValue[5] = '1';
        }
        if ("Y".equals(cdms.getArEmerveh())) {
            vehValue[7] = '1';
        }
        if ("Y".equals(cdms.getArTaxis())) {
            vehValue[8] = '1';
        }
        if ("Y".equals(cdms.getArBus())) {
            vehValue[9] = '1';
        }
        if ("Y".equals(cdms.getArCarpool())) {
            vehValue[13] = '1';
        }
        if ("Y".equals(cdms.getArDeliver())) {
            vehValue[26] = '1';
        }

        relation.setVeh(String.valueOf(vehValue));
    }

    /**
     * Process gate type logic for condition type 4
     */
    private void processGateType(RelationM relation, Cdms cdms) {
        if (cdms.getCondType() == 4 && cdms.getCondVal1() != null) {
            switch (cdms.getCondVal1()) {
                case "EMERGENCY VEHICLE ACCESS":
                    relation.setGateType("0");
                    break;
                case "KEY ACCESS":
                    relation.setGateType("1");
                    break;
                case "PERMISSION REQUIRED":
                    relation.setGateType("2");
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * Process traffic light location for condition types 11 and 16
     */
    private void processTrafficLightLocation(RelationM relation, Cdms cdms) {
        if (cdms.getCondType() == 11 || cdms.getCondType() == 16) {
            if (cdms.getCondVal1() != null) {
                char[] tlLocatVale = {'0', '0', '0'};
                switch (cdms.getCondVal1()) {
                    case "RIGHT":
                        tlLocatVale[1] = '1';
                        break;
                    case "LEFT":
                        tlLocatVale[0] = '1';
                        break;
                    case "OVERHEAD":
                        tlLocatVale[2] = '1';
                        break;
                    default:
                        break;
                }
                relation.setTlLocat(new StringBuffer(String.valueOf(tlLocatVale)).reverse().toString());
            }
        }
    }

    /**
     * Generate relation IDs in batch for better performance
     */
    private void generateRelationIds(List<RelationM> relationList) {
        try {
            List<Long> ids = inheritIDService.createID(new InheritIDDTO(12L, Long.valueOf(relationList.size())));
            for (int i = 0; i < relationList.size(); i++) {
                relationList.get(i).setRelationid(ids.get(i).toString());
            }
        } catch (Exception e) {
            log.error("Error generating relation IDs", e);
            throw new RuntimeException("Failed to generate relation IDs", e);
        }
    }

    /**
     * Save relations in batches for better performance
     */
    private void saveRelationsInBatches(List<RelationM> relationList, String area, String country) {
        if (relationList.isEmpty()) {
            return;
        }

        log.info("Saving {} relations in batches", relationList.size());

        // Set database context for saving
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }

        try {
            // Use batch insert for better performance
            relationMapper.mysqlInsertOrUpdateBath(relationList);
            log.info("Successfully saved {} relations", relationList.size());
        } catch (Exception e) {
            log.error("Error saving relations in batch", e);
            throw new RuntimeException("Failed to save relations", e);
        }
    }

    /**
     * Monitor and log memory usage during processing
     */
    private void logMemoryUsage(String phase) {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        long maxMemory = runtime.maxMemory();

        log.info("Memory Usage - {}: Used: {} MB, Free: {} MB, Total: {} MB, Max: {} MB",
                phase,
                usedMemory / (1024 * 1024),
                freeMemory / (1024 * 1024),
                totalMemory / (1024 * 1024),
                maxMemory / (1024 * 1024));
    }

    /**
     * Optimized version of ruleConvert with better memory management and performance
     * Completely rewritten business logic while maintaining 100% functional parity
     */
    @Async("optimizedAsyncTaskExecutor")
    public CompletableFuture<Void> optimizedRuleConvert(List<Cdms> cdmsList, Set<String> nodeIdSet,
                                                        String area, String country, CountDownLatch countDownLatch) {
        try {
            // Log memory usage at start
            logMemoryUsage("Start of optimizedRuleConvert");

            // Set database context
            configureDatabaseContext(area, country, true);

            // Process CDMS records and create rules
            List<RuleM> ruleList = processOptimizedRuleConversion(cdmsList, area, country);

            if (!ruleList.isEmpty()) {
                // Generate IDs and save rules in optimized batches
                saveRulesInOptimizedBatches(ruleList, area, country);
                log.info("Successfully processed {} rules", ruleList.size());
            }

            logMemoryUsage("End of optimizedRuleConvert");

        } catch (Exception e) {
            log.error("Error in optimized rule conversion", e);
            throw new RuntimeException("Failed to process optimized rule conversion", e);
        } finally {
            countDownLatch.countDown();
            // Cleanup resources
            cleanupOptimizedResources();
        }

        return CompletableFuture.completedFuture(null);
    }

    /**
     * Process CDMS records and create rules with optimized business logic
     * Rewritten from scratch while maintaining 100% functional parity
     */
    private List<RuleM> processOptimizedRuleConversion(List<Cdms> cdmsList, String area, String country) {
        List<RuleM> ruleList = new ArrayList<>();

        for (Cdms cdms : cdmsList) {
            // Only process condition types 7 and 39 (same as original logic)
            if (cdms.getCondType() == 7 || cdms.getCondType() == 39) {
                RuleM rule = createOptimizedRule(cdms, area, country);
                if (rule != null) {
                    ruleList.add(rule);
                }
            }
        }

        return ruleList;
    }

    /**
     * Create a single rule from CDMS data with optimized processing
     * Rewritten business logic maintaining exact functional parity
     */
    private RuleM createOptimizedRule(Cdms cdms, String area, String country) {
        try {
            RuleM rule = new RuleM();

            // Generate rule ID (will be replaced with inherited ID later)
            rule.setRuleId(UUID.randomUUID().toString());

            // Process IN_LINKID with database context switching
            processInLinkId(rule, cdms, area, country);

            // Process OUT_LINKID and PASS field with database context switching
            processOutLinkIdAndPass(rule, cdms, area, country);

            // Process NODE_ID with database context switching
            processNodeId(rule, cdms, area, country);

            // Set TILE_ID and TILE_TYPE
            setTileInformation(rule, area, country);

            // Process FLAG based on COND_VAL1
            setRuleFlag(rule, cdms);

            // Process VPERIOD (validity period)
            processValidityPeriod(rule, cdms, area, country);

            // Process vehicle restrictions
            processVehicleRestrictions(rule, cdms);

            // Set common fields
            setCommonRuleFields(rule,area);

            return rule;

        } catch (Exception e) {
            log.error("Error creating optimized rule for CDMS ID: {}", cdms.getCondId(), e);
            return null;
        }
    }

    /**
     * Process IN_LINKID field with proper database context - EXACT COPY of original logic
     */
    private void processInLinkId(RuleM rule, Cdms cdms, String area, String country) {
        try {
            // Switch to target database context (same as original)
            configureDatabaseContext(area, country, false);

            // Use direct LinkM lookup (exact same as original logic)
            QueryWrapper<LinkM> linkQueryWrapper = new QueryWrapper<>();
            linkQueryWrapper.eq("link_id", cdms.getLinkId().toString());
            LinkM linkM = linkMMapper.selectOne(linkQueryWrapper);

            if (linkM != null) {
                rule.setInlinkId(linkM.getHllLinkid());
            }

        } catch (Exception e) {
            log.error("Error processing IN_LINKID for CDMS: {}", cdms.getCondId(), e);
            // Fallback to original link ID
            rule.setInlinkId(cdms.getLinkId().toString());
        } finally {
            // Switch back to source database context
            configureDatabaseContext(area, country, true);
        }
    }

    /**
     * Process OUT_LINKID and PASS fields with proper database context - EXACT COPY of original logic
     */
    private void processOutLinkIdAndPass(RuleM rule, Cdms cdms, String area, String country) {
        try {
            // Switch to source database context to get RDMS data
            configureDatabaseContext(area, country, true);

            // Get RDMS data to find the out link (same as original logic)
            List<Rdms> rdmsList = rdmsService.lambdaQuery()
                .eq(Rdms::getCondId, cdms.getCondId())
                .orderByAsc(Rdms::getSeqNumber)
                .list();

            if (!rdmsList.isEmpty()) {
                // Get the last link in the sequence as out link
                String outLinkId = rdmsList.get(rdmsList.size() - 1).getManLinkid().toString();

                // Switch to target database context to get HLL link ID
                configureDatabaseContext(area, country, false);

                QueryWrapper<LinkM> outQueryMapper = new QueryWrapper<>();
                outQueryMapper.eq("link_id", outLinkId);
                LinkM linkM = linkMMapper.selectOne(outQueryMapper);

                if (linkM != null) {
                    rule.setOutlinkId(linkM.getHllLinkid());
                }

                // Process PASS field (EXACT COPY of original logic lines 508-591)
                List<String> passLinkList = new ArrayList<>();
                for (int i = 0; i < rdmsList.size() - 1; i++) {
                    passLinkList.add(rdmsList.get(i).getManLinkid().toString());
                }

                // PASS field processing
                if (passLinkList.size() == 0) {
                    rule.setPass(null);
                } else {
                    // Switch to target database context for pass link processing
                    configureDatabaseContext(area, country, false);

                    if (CollUtil.isNotEmpty(passLinkList)) {
                        QueryWrapper<LinkM> passLinkQueryWrapper = new QueryWrapper<>();
                        passLinkQueryWrapper.in("link_id", passLinkList);
                        passLinkQueryWrapper.last("order by array_position(ARRAY" + passLinkList + "::VARCHAR[],link_id)");
                        log.info("passLinkList is {}", passLinkList);
                        List<String> hllLinkIds = linkMMapper.selectList(passLinkQueryWrapper)
                            .stream().map(LinkM::getHllLinkid).collect(Collectors.toList());
                        rule.setPass(String.join("|", hllLinkIds));
                    } else {
                        rule.setPass(null);
                    }
                }
            }

        } catch (Exception e) {
            log.error("Error processing OUT_LINKID and PASS for CDMS: {}", cdms.getCondId(), e);
        } finally {
            // Switch back to source database context
            configureDatabaseContext(area, country, true);
        }
    }

    /**
     * Process NODE_ID field with proper database context
     */
    private void processNodeId(RuleM rule, Cdms cdms, String area, String country) {
        try {
            // Switch to source database context to get HerePhaStreets data
            configureDatabaseContext(area, country, true);

            // Get HerePhaStreets data to find the node (same as original logic)
            List<HerePhaStreets> herePhaStreetsList = herePhaStreetsService.lambdaQuery()
                .eq(HerePhaStreets::getLinkId, cdms.getLinkId())
                .list();

            if (!herePhaStreetsList.isEmpty()) {
                String nodeId = null;

                // Determine which node to use based on END_OF_LK field
                if ("N".equals(cdms.getEndOfLk())) {
                    nodeId = herePhaStreetsList.get(0).getNrefInId().toString();
                } else if ("R".equals(cdms.getEndOfLk())) {
                    nodeId = herePhaStreetsList.get(0).getRefInId().toString();
                }

                if (nodeId != null) {
                    // Switch to target database context to get HLL node ID
                    configureDatabaseContext(area, country, false);

                    QueryWrapper<NodeM> nodeQueryWrapper = new QueryWrapper<>();
                    nodeQueryWrapper.eq("node_id", nodeId);
                    NodeM nodeM = nodeMService.getOne(nodeQueryWrapper);

                    if (nodeM != null) {
                        rule.setNodeId(nodeM.getHllNodeid());
                    }
                }
            }

        } catch (Exception e) {
            log.error("Error processing NODE_ID for CDMS: {}", cdms.getCondId(), e);
        } finally {
            // Switch back to source database context
            configureDatabaseContext(area, country, true);
        }
    }

    /**
     * Set TILE_ID and TILE_TYPE information
     */
    private void setTileInformation(RuleM rule, String area, String country) {
        try {
            // Switch to target database context to get node geometry
            configureDatabaseContext(area, country, false);

            NodeM nodeM = nodeMService.getOne(Wrappers.<NodeM>lambdaQuery()
                .eq(NodeM::getHllNodeid, rule.getNodeId()));

            if (nodeM != null && nodeM.getGeomwkt() != null) {
                String tileId = CommonUtils.getH3IndexByCentroid(nodeM.getGeomwkt(), 7);
                rule.setTileId(tileId);
                rule.setTileType(Const.H3);
            }

        } catch (Exception e) {
            log.error("Error setting tile information for rule: {}", rule.getRuleId(), e);
        } finally {
            // Switch back to source database context
            configureDatabaseContext(area, country, true);
        }
    }

    /**
     * Set rule flag based on COND_VAL1 (exact same logic as original)
     */
    private void setRuleFlag(RuleM rule, Cdms cdms) {
        switch (cdms.getCondVal1()) {
            case "LEGAL":
            case "PHYSICAL":
                rule.setFlag(1);
                break;
            case "LOGICAL":
                rule.setFlag(2);
                break;
            case "OBSERVED":
                rule.setFlag(0);
                break;
            default:
                rule.setFlag(0);
        }

        // Special case for condition type 39
        if (cdms.getCondType() == 39) {
            rule.setFlag(3);
        }
    }

    /**
     * Process validity period (VPERIOD) with database context switching
     * COMPLETE REWRITE maintaining 100% functional parity with original implementation
     */
    private void processValidityPeriod(RuleM rule, Cdms cdms, String area, String country) {
        try {
            // Switch to source database context for CDMS DTMod query
            configureDatabaseContext(area, country, true);

            List<Cdmsdtmod> cdmsDtmodList = cdmsdtmodService.lambdaQuery()
                .eq(Cdmsdtmod::getCondId, cdms.getCondId())
                .list();

            if (!cdmsDtmodList.isEmpty()) {
                List<String> periodValueList = new ArrayList<>();

                for (Cdmsdtmod cdmsdtmod : cdmsDtmodList) {
                    String dttmeType = cdmsdtmod.getDttmeType();

                    if ("A".equals(dttmeType)) {
                        String periodValue = processTypeA(cdmsdtmod);
                        if (!periodValue.isEmpty()) {
                            periodValueList.add(periodValue);
                        }
                    } else if ("1".equals(dttmeType)) {
                        String periodValue = processType1(cdmsdtmod);
                        if (!periodValue.isEmpty()) {
                            periodValueList.add(periodValue);
                        }
                    } else if ("C".equals(dttmeType)) {
                        String periodValue = processTypeC(cdmsdtmod);
                        if (!periodValue.isEmpty()) {
                            periodValueList.add(periodValue);
                        }
                    } else if ("D".equals(dttmeType)) {
                        String periodValue = processTypeD(cdmsdtmod);
                        if (!periodValue.isEmpty()) {
                            periodValueList.add(periodValue);
                        }
                    } else if ("E".equals(dttmeType)) {
                        String periodValue = processTypeE(cdmsdtmod);
                        if (!periodValue.isEmpty()) {
                            periodValueList.add(periodValue);
                        }
                    } else if ("F".equals(dttmeType)) {
                        String periodValue = processTypeF(cdmsdtmod);
                        if (!periodValue.isEmpty()) {
                            periodValueList.add(periodValue);
                        }
                    } else if ("H".equals(dttmeType)) {
                        String periodValue = processTypeH(cdmsdtmod);
                        if (!periodValue.isEmpty()) {
                            periodValueList.add(periodValue);
                        }
                    } else if ("I".equals(dttmeType)) {
                        String periodValue = processTypeI(cdmsdtmod);
                        if (!periodValue.isEmpty()) {
                            periodValueList.add(periodValue);
                        }
                    }
                }

                if (!periodValueList.isEmpty()) {
                    log.info("periodValueResult is:" + String.join("|", periodValueList));
                    rule.setVperiod(String.join("|", periodValueList));
                }
            }

        } catch (Exception e) {
            log.error("Error processing validity period for CDMS: {}", cdms.getCondId(), e);
        }
    }

    /**
     * Process dttmeType "A" - Exact copy of original logic
     */
    private String processTypeA(Cdmsdtmod cdmsdtmod) {
        String periodValue = "";
        String timeValue = "", startTimeValue = "", endTimeValue = "";
        String dateValue = "", startDateValue = "", endDateValue = "";

        // startDateValue
        if (cdmsdtmod.getRefDate() != null) {
            if (CommonUtils.isNumeric(cdmsdtmod.getRefDate())) {
                startDateValue = "y" + cdmsdtmod.getRefDate().substring(0, 4)
                        + "M" + cdmsdtmod.getRefDate().substring(4, 6)
                        + "d" + cdmsdtmod.getRefDate().substring(6, 8);
            }
        }
        // endDateValue
        if (cdmsdtmod.getExpDate() != null) {
            if (CommonUtils.isNumeric(cdmsdtmod.getExpDate())) {
                endDateValue = "y" + cdmsdtmod.getExpDate().substring(0, 4)
                        + "M" + cdmsdtmod.getExpDate().substring(4, 6)
                        + "d" + cdmsdtmod.getExpDate().substring(6, 8);
            }
        }

        // startTimeValue
        if (cdmsdtmod.getStarttime() != null) {
            if (cdmsdtmod.getStarttime().length() > 0) {
                int hour = Integer.parseInt(cdmsdtmod.getStarttime().substring(0, 2));
                int minitue = Integer.parseInt(cdmsdtmod.getStarttime().substring(2, 4));
                if (minitue > 0) {
                    startTimeValue += "h" + hour + "m" + minitue;
                } else {
                    startTimeValue += "h" + hour;
                }
            }
        }

        // endTimeValue
        if (cdmsdtmod.getEndtime() != null) {
            if (cdmsdtmod.getEndtime().length() > 0) {
                int hour = Integer.parseInt(cdmsdtmod.getEndtime().substring(0, 2));
                int minitue = Integer.parseInt(cdmsdtmod.getEndtime().substring(2, 4));
                if (minitue > 0) {
                    endTimeValue += "h" + hour + "m" + minitue;
                } else {
                    endTimeValue += "h" + hour;
                }
            }
        }

        // combine date string
        if (startDateValue.length() > 0) {
            dateValue = "[(" + startDateValue + ")";
            if (endDateValue.length() > 0) {
                dateValue += "(" + endDateValue + ")]";
            } else {
                dateValue += "]";
            }
        } else {
            if (endDateValue.length() > 0) {
                dateValue = "[()(" + endDateValue + ")]";
            } else {
                dateValue = "";
            }
        }

        // combine time string
        if (startTimeValue.length() > 0) {
            timeValue = "[(" + startTimeValue + ")";
            if (endTimeValue.length() > 0) {
                timeValue += "(" + endTimeValue + ")]";
            } else {
                timeValue += "]";
            }
        } else {
            if (endTimeValue.length() > 0) {
                timeValue = "[()(" + endTimeValue + ")]";
            } else {
                timeValue = "";
            }
        }

        // combine final period value
        if (dateValue.length() > 0) {
            periodValue = dateValue;
            if (timeValue.length() > 0) {
                periodValue += "*" + timeValue;
            } else {
                periodValue += "";
            }
        } else {
            if (timeValue.length() > 0) {
                periodValue = timeValue;
            } else {
                periodValue = "";
            }
        }

        log.info("periodValue:" + periodValue);
        return periodValue;
    }

    /**
     * Process dttmeType "1" - Exact copy of original logic
     */
    private String processType1(Cdmsdtmod cdmsdtmod) {
        String periodValue = "";
        String timeValue = "", startTimeValue = "", endTimeValue = "";
        String tValue = "", startTvalue = "", endTvalue = "";

        // startTvalue
        if (cdmsdtmod.getRefDate() != null) {
            if (cdmsdtmod.getRefDate().length() == 7) {
                if ("Y".equals(cdmsdtmod.getRefDate().split("")[0])) {
                    startTvalue += "t1";
                }
                if ("Y".equals(cdmsdtmod.getRefDate().split("")[1])) {
                    startTvalue += "t2";
                }
                if ("Y".equals(cdmsdtmod.getRefDate().split("")[2])) {
                    startTvalue += "t3";
                }
                if ("Y".equals(cdmsdtmod.getRefDate().split("")[3])) {
                    startTvalue += "t4";
                }
                if ("Y".equals(cdmsdtmod.getRefDate().split("")[4])) {
                    startTvalue += "t5";
                }
                if ("Y".equals(cdmsdtmod.getRefDate().split("")[5])) {
                    startTvalue += "t6";
                }
                if ("Y".equals(cdmsdtmod.getRefDate().split("")[6])) {
                    startTvalue += "t7";
                }
            }
        }
        // endTvalue
        if (cdmsdtmod.getExpDate() != null) {
            if (cdmsdtmod.getExpDate().length() > 0 && cdmsdtmod.getExpDate().length() == 7) {
                if ("Y".equals(cdmsdtmod.getExpDate().split("")[0])) {
                    endTvalue += "t1";
                }
                if ("Y".equals(cdmsdtmod.getExpDate().split("")[1])) {
                    endTvalue += "t2";
                }
                if ("Y".equals(cdmsdtmod.getExpDate().split("")[2])) {
                    endTvalue += "t3";
                }
                if ("Y".equals(cdmsdtmod.getExpDate().split("")[3])) {
                    endTvalue += "t4";
                }
                if ("Y".equals(cdmsdtmod.getExpDate().split("")[4])) {
                    endTvalue += "t5";
                }
                if ("Y".equals(cdmsdtmod.getExpDate().split("")[5])) {
                    endTvalue += "t6";
                }
                if ("Y".equals(cdmsdtmod.getExpDate().split("")[6])) {
                    endTvalue += "t7";
                }
            }
        }

        // startTimeValue
        if (cdmsdtmod.getStarttime() != null) {
            if (cdmsdtmod.getStarttime().length() > 0) {
                String hour = cdmsdtmod.getStarttime().substring(0, 2);
                String minitue = cdmsdtmod.getStarttime().substring(2, 4);
                if (Integer.parseInt(minitue) > 0) {
                    startTimeValue += "h" + hour + "m" + minitue;
                } else {
                    startTimeValue += "h" + hour;
                }
            }
        }

        // endTimeValue
        if (cdmsdtmod.getEndtime() != null) {
            if (cdmsdtmod.getEndtime().length() > 0) {
                String hour = cdmsdtmod.getEndtime().substring(0, 2);
                String minitue = cdmsdtmod.getEndtime().substring(2, 4);
                if (Integer.parseInt(minitue) > 0) {
                    endTimeValue += "h" + hour + "m" + minitue;
                } else {
                    endTimeValue += "h" + hour;
                }
            }
        }

        // combine time string
        if (startTimeValue.length() > 0) {
            timeValue = "[(" + startTimeValue + ")";
            if (endTimeValue.length() > 0) {
                timeValue += "(" + endTimeValue + ")]";
            } else {
                timeValue += "]";
            }
        } else {
            if (endTimeValue.length() > 0) {
                timeValue = "[()(" + endTimeValue + ")]";
            } else {
                timeValue = "";
            }
        }

        // combine t string
        if (startTvalue.length() > 0) {
            tValue = "(" + startTvalue + ")";
            if (endTvalue.length() > 0) {
                tValue += "(" + endTvalue + ")";
            } else {
                tValue += "";
            }
        } else {
            if (endTvalue.length() > 0) {
                tValue = "()(" + endTvalue + ")";
            } else {
                tValue = "";
            }
        }

        // combine final period value
        if (timeValue.length() > 0) {
            periodValue = timeValue;
            if (tValue.length() > 0) {
                periodValue += "*" + tValue;
            } else {
                periodValue += "";
            }
        } else {
            if (tValue.length() > 0) {
                periodValue = tValue;
            } else {
                periodValue = "";
            }
        }

        return periodValue;
    }

    /**
     * Process dttmeType "C" - Exact copy of original logic
     */
    private String processTypeC(Cdmsdtmod cdmsdtmod) {
        String periodValue = "";
        String timeValue = "", startTimeValue = "", endTimeValue = "";
        String dateValue = "", startDateValue = "", endDateValue = "";

        if (cdmsdtmod.getRefDate() != null) {
            //startDateValue
            startDateValue = "d" + cdmsdtmod.getRefDate().substring(2, 4);
            //endDateValue
            endDateValue = "d" + cdmsdtmod.getExpDate().substring(2, 4);

            // startTimeValue
            if (cdmsdtmod.getStarttime() != null) {
                if (cdmsdtmod.getStarttime().length() > 0) {
                    String hour = cdmsdtmod.getStarttime().substring(0, 2);
                    String minitue = cdmsdtmod.getStarttime().substring(2, 4);
                    if (Integer.parseInt(minitue) > 0) {
                        startTimeValue += "h" + hour + "m" + minitue;
                    } else {
                        startTimeValue += "h" + hour;
                    }
                }
            }

            // endTimeValue
            if (cdmsdtmod.getEndtime() != null) {
                if (cdmsdtmod.getEndtime().length() > 0) {
                    String hour = cdmsdtmod.getEndtime().substring(0, 2);
                    String minitue = cdmsdtmod.getEndtime().substring(2, 4);
                    if (Integer.parseInt(minitue) > 0) {
                        endTimeValue += "h" + hour + "m" + minitue;
                    } else {
                        endTimeValue += "h" + hour;
                    }
                }
            }

            if (startTimeValue.length() > 0) {
                timeValue = "[(" + startTimeValue + ")";
                if (endTimeValue.length() > 0) {
                    timeValue += "(" + endTimeValue + ")]";
                } else {
                    timeValue += "]";
                }
            } else {
                if (endTimeValue.length() > 0) {
                    timeValue = "[()(" + endTimeValue + ")]";
                } else {
                    timeValue = "";
                }
            }
            // combine time string
            if (startDateValue.length() > 0) {
                dateValue = "[(" + startDateValue + ")";
                if (endDateValue.length() > 0) {
                    dateValue += "(" + endDateValue + ")]";
                } else {
                    dateValue += "]";
                }
            } else {
                if (endDateValue.length() > 0) {
                    dateValue = "[()(" + endDateValue + ")]";
                } else {
                    dateValue = "";
                }
            }

            if (dateValue.length() > 0) {
                periodValue = dateValue;
                if (timeValue.length() > 0) {
                    periodValue += "*" + timeValue;
                } else {
                    periodValue += "";
                }
            } else {
                if (timeValue.length() > 0) {
                    periodValue = timeValue;
                } else {
                    periodValue = "";
                }
            }
        }
        return periodValue;
    }

    /**
     * Process dttmeType "D" - Exact copy of original logic
     */
    private String processTypeD(Cdmsdtmod cdmsdtmod) {
        String periodValue = "";
        String timeValue = "", startTimeValue = "", endTimeValue = "";
        String dateValue = "", startDateValue = "", endDateValue = "";

        if (cdmsdtmod.getRefDate() != null) {
            //startDateValue
            startDateValue = "w" + cdmsdtmod.getRefDate().substring(6, 8) + "d" + cdmsdtmod.getRefDate().substring(2, 4);
            //endDateValue
            endDateValue = "w" + cdmsdtmod.getRefDate().substring(6, 8) + "d" + cdmsdtmod.getExpDate().substring(2, 4);

            // startTimeValue
            if (cdmsdtmod.getStarttime() != null) {
                if (cdmsdtmod.getStarttime().length() > 0) {
                    String hour = cdmsdtmod.getStarttime().substring(0, 2);
                    String minitue = cdmsdtmod.getStarttime().substring(2, 4);
                    if (Integer.parseInt(minitue) > 0) {
                        startTimeValue += "h" + hour + "m" + minitue;
                    } else {
                        startTimeValue += "h" + hour;
                    }
                }
            }

            // endTimeValue
            if (cdmsdtmod.getEndtime() != null) {
                if (cdmsdtmod.getEndtime().length() > 0) {
                    String hour = cdmsdtmod.getEndtime().substring(0, 2);
                    String minitue = cdmsdtmod.getEndtime().substring(2, 4);
                    if (Integer.parseInt(minitue) > 0) {
                        endTimeValue += "h" + hour + "m" + minitue;
                    } else {
                        endTimeValue += "h" + hour;
                    }
                }
            }

            if (startTimeValue.length() > 0) {
                timeValue = "[(" + startTimeValue + ")";
                if (endTimeValue.length() > 0) {
                    timeValue += "(" + endTimeValue + ")]";
                } else {
                    timeValue += "]";
                }
            } else {
                if (endTimeValue.length() > 0) {
                    timeValue = "[()(" + endTimeValue + ")]";
                } else {
                    timeValue = "";
                }
            }
            // combine time string
            if (startDateValue.length() > 0) {
                dateValue = "[(" + startDateValue + ")";
                if (endDateValue.length() > 0) {
                    dateValue += "(" + endDateValue + ")]";
                } else {
                    dateValue += "]";
                }
            } else {
                if (endDateValue.length() > 0) {
                    dateValue = "[()(" + endDateValue + ")]";
                } else {
                    dateValue = "";
                }
            }

            if (dateValue.length() > 0) {
                periodValue = dateValue;
                if (timeValue.length() > 0) {
                    periodValue += "*" + timeValue;
                } else {
                    periodValue += "";
                }
            } else {
                if (timeValue.length() > 0) {
                    periodValue = timeValue;
                } else {
                    periodValue = "";
                }
            }
        }
        return periodValue;
    }

    /**
     * Process dttmeType "E" - Exact copy of original logic
     */
    private String processTypeE(Cdmsdtmod cdmsdtmod) {
        String periodValue = "";
        String timeValue = "", startTimeValue = "", endTimeValue = "";
        String dateValue = "", startDateValue = "", endDateValue = "";

        if (cdmsdtmod.getRefDate() != null) {
            //startDateValue
            startDateValue = "W" + cdmsdtmod.getRefDate().substring(6, 8) + "d" + cdmsdtmod.getRefDate().substring(2, 4);
            //endDateValue
            endDateValue = "W" + cdmsdtmod.getRefDate().substring(6, 8) + "d" + cdmsdtmod.getExpDate().substring(2, 4);

            // startTimeValue
            if (cdmsdtmod.getStarttime() != null) {
                if (cdmsdtmod.getStarttime().length() > 0) {
                    String hour = cdmsdtmod.getStarttime().substring(0, 2);
                    String minitue = cdmsdtmod.getStarttime().substring(2, 4);
                    if (Integer.parseInt(minitue) > 0) {
                        startTimeValue += "h" + hour + "m" + minitue;
                    } else {
                        startTimeValue += "h" + hour;
                    }
                }
            }

            // endTimeValue
            if (cdmsdtmod.getEndtime() != null) {
                if (cdmsdtmod.getEndtime().length() > 0) {
                    String hour = cdmsdtmod.getEndtime().substring(0, 2);
                    String minitue = cdmsdtmod.getEndtime().substring(2, 4);
                    if (Integer.parseInt(minitue) > 0) {
                        endTimeValue += "h" + hour + "m" + minitue;
                    } else {
                        endTimeValue += "h" + hour;
                    }
                }
            }

            if (startTimeValue.length() > 0) {
                timeValue = "[(" + startTimeValue + ")";
                if (endTimeValue.length() > 0) {
                    timeValue += "(" + endTimeValue + ")]";
                } else {
                    timeValue += "]";
                }
            } else {
                if (endTimeValue.length() > 0) {
                    timeValue = "[()(" + endTimeValue + ")]";
                } else {
                    timeValue = "";
                }
            }
            // combine time string
            if (startDateValue.length() > 0) {
                dateValue = "[(" + startDateValue + ")";
                if (endDateValue.length() > 0) {
                    dateValue += "(" + endDateValue + ")]";
                } else {
                    dateValue += "]";
                }
            } else {
                if (endDateValue.length() > 0) {
                    dateValue = "[()(" + endDateValue + ")]";
                } else {
                    dateValue = "";
                }
            }

            if (dateValue.length() > 0) {
                periodValue = dateValue;
                if (timeValue.length() > 0) {
                    periodValue += "*" + timeValue;
                } else {
                    periodValue += "";
                }
            } else {
                if (timeValue.length() > 0) {
                    periodValue = timeValue;
                } else {
                    periodValue = "";
                }
            }
        }
        return periodValue;
    }

    /**
     * Process dttmeType "F" - Exact copy of original logic
     */
    private String processTypeF(Cdmsdtmod cdmsdtmod) {
        String periodValue = "";
        String timeValue = "", startTimeValue = "", endTimeValue = "";
        String dateValue = "", startDateValue = "", endDateValue = "";

        if (cdmsdtmod.getRefDate() != null) {
            //startDateValue
            startDateValue = "w" + cdmsdtmod.getRefDate().substring(2, 4);
            //endDateValue
            endDateValue = "w" + cdmsdtmod.getExpDate().substring(2, 4);

            // startTimeValue
            if (cdmsdtmod.getStarttime() != null) {
                if (cdmsdtmod.getStarttime().length() > 0) {
                    int hour = Integer.parseInt(cdmsdtmod.getStarttime().substring(0, 2));
                    int minitue = Integer.parseInt(cdmsdtmod.getStarttime().substring(2, 4));
                    if (minitue > 0) {
                        startTimeValue += "h" + hour + "m" + minitue;
                    } else {
                        startTimeValue += "h" + hour;
                    }
                }
            }

            // endTimeValue
            if (cdmsdtmod.getEndtime() != null) {
                if (cdmsdtmod.getEndtime().length() > 0) {
                    String hour = cdmsdtmod.getEndtime().substring(0, 2);
                    String minitue = cdmsdtmod.getEndtime().substring(2, 4);
                    if (Integer.parseInt(minitue) > 0) {
                        endTimeValue += "h" + hour + "m" + minitue;
                    } else {
                        endTimeValue += "h" + hour;
                    }
                }
            }

            if (startTimeValue.length() > 0) {
                timeValue = "[(" + startTimeValue + ")";
                if (endTimeValue.length() > 0) {
                    timeValue += "(" + endTimeValue + ")]";
                } else {
                    timeValue += "]";
                }
            } else {
                if (endTimeValue.length() > 0) {
                    timeValue = "[()(" + endTimeValue + ")]";
                } else {
                    timeValue = "";
                }
            }
            // combine time string
            if (startDateValue.length() > 0) {
                dateValue = "[(" + startDateValue + ")";
                if (endDateValue.length() > 0) {
                    dateValue += "(" + endDateValue + ")]";
                } else {
                    dateValue += "]";
                }
            } else {
                if (endDateValue.length() > 0) {
                    dateValue = "[()(" + endDateValue + ")]";
                } else {
                    dateValue = "";
                }
            }

            if (dateValue.length() > 0) {
                periodValue = dateValue;
                if (timeValue.length() > 0) {
                    periodValue += "*" + timeValue;
                } else {
                    periodValue += "";
                }
            } else {
                if (timeValue.length() > 0) {
                    periodValue = timeValue;
                } else {
                    periodValue = "";
                }
            }
        }
        return periodValue;
    }

    /**
     * Process dttmeType "H" - Exact copy of original logic
     */
    private String processTypeH(Cdmsdtmod cdmsdtmod) {
        String periodValue = "";
        String timeValue = "", startTimeValue = "", endTimeValue = "";
        String dateValue = "", startDateValue = "", endDateValue = "";

        if (cdmsdtmod.getRefDate() != null) {
            //startDateValue
            startDateValue = "M" + cdmsdtmod.getRefDate().substring(2, 4);
            //endDateValue
            endDateValue = "M" + cdmsdtmod.getExpDate().substring(2, 4);

            // startTimeValue
            if (cdmsdtmod.getStarttime() != null) {
                if (cdmsdtmod.getStarttime().length() > 0) {
                    String hour = cdmsdtmod.getStarttime().substring(0, 2);
                    String minitue = cdmsdtmod.getStarttime().substring(2, 4);
                    if (Integer.parseInt(minitue) > 0) {
                        startTimeValue += "h" + hour + "m" + minitue;
                    } else {
                        startTimeValue += "h" + hour;
                    }
                }
            }

            // endTimeValue
            if (cdmsdtmod.getEndtime() != null) {
                if (cdmsdtmod.getEndtime().length() > 0) {
                    String hour = cdmsdtmod.getEndtime().substring(0, 2);
                    String minitue = cdmsdtmod.getEndtime().substring(2, 4);
                    if (Integer.parseInt(minitue) > 0) {
                        endTimeValue += "h" + hour + "m" + minitue;
                    } else {
                        endTimeValue += "h" + hour;
                    }
                }
            }

            if (startTimeValue.length() > 0) {
                timeValue = "[(" + startTimeValue + ")";
                if (endTimeValue.length() > 0) {
                    timeValue += "(" + endTimeValue + ")]";
                } else {
                    timeValue += "]";
                }
            } else {
                if (endTimeValue.length() > 0) {
                    timeValue = "[()(" + endTimeValue + ")]";
                } else {
                    timeValue = "";
                }
            }
            // combine time string
            if (startDateValue.length() > 0) {
                dateValue = "[(" + startDateValue + ")";
                if (endDateValue.length() > 0) {
                    dateValue += "(" + endDateValue + ")]";
                } else {
                    dateValue += "]";
                }
            } else {
                if (endDateValue.length() > 0) {
                    dateValue = "[()(" + endDateValue + ")]";
                } else {
                    dateValue = "";
                }
            }

            if (dateValue.length() > 0) {
                periodValue = dateValue;
                if (timeValue.length() > 0) {
                    periodValue += "*" + timeValue;
                } else {
                    periodValue += "";
                }
            } else {
                if (timeValue.length() > 0) {
                    periodValue = timeValue;
                } else {
                    periodValue = "";
                }
            }
        }
        return periodValue;
    }

    /**
     * Process dttmeType "I" - Exact copy of original logic
     */
    private String processTypeI(Cdmsdtmod cdmsdtmod) {
        String periodValue = "";
        String timeValue = "", startTimeValue = "", endTimeValue = "";
        String dateValue = "", startDateValue = "", endDateValue = "";

        if (cdmsdtmod.getRefDate() != null) {
            //startDateValue
            startDateValue = "M" + cdmsdtmod.getRefDate().substring(6, 8) + "d" + cdmsdtmod.getRefDate().substring(2, 4);
            //endDateValue
            endDateValue = "M" + cdmsdtmod.getRefDate().substring(6, 8) + "d" + cdmsdtmod.getExpDate().substring(2, 4);

            // startTimeValue
            if (cdmsdtmod.getStarttime() != null) {
                if (cdmsdtmod.getStarttime().length() > 0) {
                    String hour = cdmsdtmod.getStarttime().substring(0, 2);
                    String minitue = cdmsdtmod.getStarttime().substring(2, 4);
                    if (Integer.parseInt(minitue) > 0) {
                        startTimeValue += "h" + hour + "m" + minitue;
                    } else {
                        startTimeValue += "h" + hour;
                    }
                }
            }

            // endTimeValue
            if (cdmsdtmod.getEndtime() != null) {
                if (cdmsdtmod.getEndtime().length() > 0) {
                    String hour = cdmsdtmod.getEndtime().substring(0, 2);
                    String minitue = cdmsdtmod.getEndtime().substring(2, 4);
                    if (Integer.parseInt(minitue) > 0) {
                        endTimeValue += "h" + hour + "m" + minitue;
                    } else {
                        endTimeValue += "h" + hour;
                    }
                }
            }

            if (startTimeValue.length() > 0) {
                timeValue = "[(" + startTimeValue + ")";
                if (endTimeValue.length() > 0) {
                    timeValue += "(" + endTimeValue + ")]";
                } else {
                    timeValue += "]";
                }
            } else {
                if (endTimeValue.length() > 0) {
                    timeValue = "[()(" + endTimeValue + ")]";
                } else {
                    timeValue = "";
                }
            }
            // combine time string
            if (startDateValue.length() > 0) {
                dateValue = "[(" + startDateValue + ")";
                if (endDateValue.length() > 0) {
                    dateValue += "(" + endDateValue + ")]";
                } else {
                    dateValue += "]";
                }
            } else {
                if (endDateValue.length() > 0) {
                    dateValue = "[()(" + endDateValue + ")]";
                } else {
                    dateValue = "";
                }
            }

            if (dateValue.length() > 0) {
                periodValue = dateValue;
                if (timeValue.length() > 0) {
                    periodValue += "*" + timeValue;
                } else {
                    periodValue += "";
                }
            } else {
                if (timeValue.length() > 0) {
                    periodValue = timeValue;
                } else {
                    periodValue = "";
                }
            }
        }
        return periodValue;
    }

    /**
     * Process vehicle restrictions (exact same logic as original)
     */
    private void processVehicleRestrictions(RuleM rule, Cdms cdms) {
        char[] vehValue = {'0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0',
                          '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0',
                          '0', '0', '0'};

        // Set vehicle restriction flags based on CDMS fields (exact same logic)
        if ("Y".equals(cdms.getArAuto())) {
            vehValue[0] = '1';
        }
        if ("Y".equals(cdms.getArTrucks())) {
            vehValue[2] = '1';
        }
        if ("Y".equals(cdms.getArMotor())) {
            vehValue[5] = '1';
        }
        if ("Y".equals(cdms.getArEmerveh())) {
            vehValue[7] = '1';
        }
        if ("Y".equals(cdms.getArTaxis())) {
            vehValue[8] = '1';
        }
        if ("Y".equals(cdms.getArBus())) {
            vehValue[9] = '1';
        }
        if ("Y".equals(cdms.getArCarpool())) {
            vehValue[13] = '1';
        }
        if ("Y".equals(cdms.getArDeliver())) {
            vehValue[26] = '1';
        }

        rule.setVehclType(String.valueOf(vehValue));
    }

    /**
     * Set common rule fields (exact same logic as original)
     */
    private void setCommonRuleFields(RuleM rule,String area) {
        rule.setUpDate(LocalDateTime.now());
        rule.setDatasource("7");
        rule.setStatus(0);
        if (StrUtil.isNotBlank(area)) {
            rule.setArea(Integer.parseInt(area.replaceAll("\\D+", "")));
        } else {
            rule.setArea(0);
        }
    }

    /**
     * Configure database context for area and country
     */
    private void configureDatabaseContext(String area, String country, boolean isSource) {
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, isSource));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
    }

    /**
     * Save rules in optimized batches with proper memory management
     */
    private void saveRulesInOptimizedBatches(List<RuleM> ruleList, String area, String country) {
        try {
            // Switch to target database context for saving
            configureDatabaseContext(area, country, false);

            // Generate inherited IDs for all rules at once
            List<Long> ids = inheritIDService.createID(new InheritIDDTO(12L, Long.valueOf(ruleList.size())));

            // Assign generated IDs to rules
            for (int i = 0; i < ruleList.size(); i++) {
                ruleList.get(i).setRuleId(ids.get(i).toString());
            }

            // Calculate optimal batch size for database operations
            int fieldCount = BeanUtil.beanToMap(new RuleM()).keySet().size();
            int maxBatchSize = 32767 / fieldCount;
            int optimizedBatchSize = Math.min(maxBatchSize, 500); // Cap for memory management

            // Save in optimized batches
            List<List<RuleM>> batches = Lists.partition(ruleList, optimizedBatchSize);
            log.info("Saving {} rules in {} batches of size {}", ruleList.size(), batches.size(), optimizedBatchSize);

            for (List<RuleM> batch : batches) {
                ruleMapper.mysqlInsertOrUpdateBath(batch);
            }

        } catch (Exception e) {
            log.error("Error saving rules in optimized batches", e);
            throw new RuntimeException("Failed to save rules", e);
        }
    }



    /**
     * Cleanup resources for optimized methods
     */
    private void cleanupOptimizedResources() {
        // Clear any thread-local variables
        DynamicDataSourceContextHolder.clear();
        MybatisPlusConfig.myTableName.remove();

        // Suggest garbage collection
        System.gc();

        log.debug("Optimized resources cleaned up and garbage collection suggested");
    }

    /**
     * Cleanup resources and force garbage collection
     */
    private void cleanupResources() {
        // Clear any thread-local variables
        DynamicDataSourceContextHolder.clear();

        // Suggest garbage collection
        System.gc();

        log.debug("Resources cleaned up and garbage collection suggested");
    }
}