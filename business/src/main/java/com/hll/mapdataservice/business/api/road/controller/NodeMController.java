package com.hll.mapdataservice.business.api.road.controller;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.hll.mapdataservice.business.api.road.service.NodeMServiceImpl;
import com.hll.mapdataservice.business.interceptor.MybatisPlusConfig;
import com.hll.mapdataservice.common.ResponseResult;
import com.hll.mapdataservice.common.entity.Node;
import com.hll.mapdataservice.common.entity.NodeM;
import com.hll.mapdataservice.common.mapper.NodeMMapper;
import com.hll.mapdataservice.common.mapper.NodeMapper;
import com.hll.mapdataservice.common.service.INodeMService;
import com.hll.mapdataservice.common.service.INodeService;
import com.hll.mapdataservice.common.utils.CommonUtils;
import com.hll.mapdataservice.business.api.road.service.OptimizedNodeMService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-11
 */
@RestController
@Slf4j
@RequestMapping("/common/node")
public class NodeMController {

    @Resource
    INodeService nodeService;
    @Resource
    NodeMServiceImpl nodeMService;

    @Resource
    NodeMapper nodeMapper;

    @Resource
    NodeMMapper nodeMMapper;

    @Resource
    OptimizedNodeMService optimizedNodeMService;

    @ApiOperation("map node diff column to node_rp")
    @GetMapping("convert2rp")
    public ResponseResult<Boolean> convert2rp(@RequestParam(value = "step", required = false, defaultValue = "1") int step,
                                              @RequestParam(value = "area", required = false, defaultValue = "") String area,
                                              @RequestParam(value = "country", required = false, defaultValue = "") String country) throws InterruptedException {
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        TimeInterval timer = DateUtil.timer();
        int nodeCount = nodeMService.count();
        int loop = nodeCount % step != 0 ? (nodeCount / step) + 1 : nodeCount / step;
        CountDownLatch countDownLatch = new CountDownLatch(loop);
        for (int i = 0; i < loop; i++) {
            List<NodeM> nodeList = nodeMService.lambdaQuery().ne(NodeM::getStatus, 1)
                    .orderByDesc(NodeM::getNodeId).last("limit " + step + " offset " + i * step).list();
            nodeService.convert2rp(area, country, countDownLatch, nodeList);
        }
        countDownLatch.await();
        log.info("map node diff column to node_rp cost time is {}s", timer.intervalSecond());
        return ResponseResult.OK(true, true);
    }


    @ApiOperation("hande node_rp mainnodeid subnodeid subnodeid2")
    @GetMapping("handleNodeRpId")
    public ResponseResult<Boolean> handleNodeRpId(@RequestParam(value = "step", required = false, defaultValue = "1400") int step,
                                                  @RequestParam(value = "area", required = false, defaultValue = "") String area,
                                                  @RequestParam(value = "country", required = false, defaultValue = "") String country) throws InterruptedException {
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        TimeInterval timer = DateUtil.timer();
        // int nodeCount = nodeRpService.lambdaQuery().last("where mainnodeid is not null or subnodeid is not null or subnodeid2 is not null").count();
        int nodeCount = nodeService.lambdaQuery().isNotNull(Node::getMainnodeid).or().isNotNull(Node::getSubnodeid).or().isNotNull(Node::getSubnodeid2).count();
        int loop = nodeCount % step != 0 ? (nodeCount / step) + 1 : nodeCount / step;
        CountDownLatch countDownLatch = new CountDownLatch(loop);
        for (int i = 0; i < loop; i++) {
            // List<Node> nodeList = nodeRpService.lambdaQuery().last("where length(mainnodeid) != 18 ")
            //         .orderByDesc(Node::getMainnodeid).last(" limit " + step + " offset " + i * step).list();
            List<Node> nodeRpList = nodeService.lambdaQuery().isNotNull(Node::getMainnodeid)
                                                            .or().isNotNull(Node::getSubnodeid).
                                                             or().isNotNull(Node::getSubnodeid2).
                                                                  orderByDesc(Node::getHllNodeid).last(" limit " + step + " offset " + i * step).list();
            // List<Node> nodeList = nodeRpMapper.selectUnhandleIds(step, i * step);
            nodeService.handleId(area, country, countDownLatch, nodeRpList);
        }
        countDownLatch.await();
        log.info("handle node_rp mainnodeid subnodeid subnodeid2 to be inherited, cost time is {}s,country is {},area is {}", timer.intervalSecond(), country, area);
        return ResponseResult.OK(true, true);
    }


    @ApiOperation("hande node mainnodeid subnodeid subnodeid2")
    @GetMapping("handleNodeId")
    public ResponseResult<Boolean> handleNodeId(@RequestParam(value = "step", required = false, defaultValue = "1400") int step,
                                                @RequestParam(value = "area", required = false, defaultValue = "") String area,
                                                @RequestParam(value = "country", required = false, defaultValue = "") String country) throws InterruptedException {
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        TimeInterval timer = DateUtil.timer();
        // int nodeCount = nodeService.lambdaQuery().last("where mainnodeid is not null or subnodeid is not null or subnodeid2 is not null").count();
        int nodeCount = nodeMService.lambdaQuery().isNotNull(NodeM::getMainnodeid).or().isNotNull(NodeM::getSubnodeid).or().isNotNull(NodeM::getSubnodeid2).count();
        int loop = nodeCount % step != 0 ? (nodeCount / step) + 1 : nodeCount / step;
        CountDownLatch countDownLatch = new CountDownLatch(loop);
        for (int i = 0; i < loop; i++) {
            List<NodeM> nodeList = nodeMService.lambdaQuery().isNotNull(NodeM::getMainnodeid)
                                                      .or().isNotNull(NodeM::getSubnodeid)
                                                      .or().isNotNull(NodeM::getSubnodeid2)
                                                      .orderByDesc(NodeM::getHllNodeid).last(" limit " + step + " offset " + i * step).list();
            // List<NodeM> nodeList = nodeMapper.selectUnhandleIds(step, i * step);
            nodeMService.handleId(area, country, countDownLatch, nodeList);
        }
        countDownLatch.await();
        log.info("handle node_m mainnodeid subnodeid subnodeid2 to be inherited, cost time is {}s,country is {},area is {}", timer.intervalSecond(), country, area);
        return ResponseResult.OK(true, true);
    }

    @ApiOperation("handle node mainnodeid subnodeid subnodeid2 optimized - NEW OPTIMIZED VERSION")
    @GetMapping("handleNodeId-optimized")
    public ResponseResult<Boolean> handleNodeIdOptimized(@RequestParam(value = "step", required = false, defaultValue = "0") int step,
                                                        @RequestParam(value = "area", required = false, defaultValue = "") String area,
                                                        @RequestParam(value = "country", required = false, defaultValue = "") String country) throws InterruptedException {

        log.info("Starting optimized node ID handling for area: {}, country: {}", area, country);
        TimeInterval timer = DateUtil.timer();

        // Configure database context
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }

        // Get total record count for adaptive processing
        int nodeCount = nodeMService.lambdaQuery()
                .isNotNull(NodeM::getMainnodeid)
                .or().isNotNull(NodeM::getSubnodeid)
                .or().isNotNull(NodeM::getSubnodeid2)
                .count();

        log.info("Total nodes to be processed: {}", nodeCount);

        // Calculate optimal step size if not provided
        int optimizedStep = calculateOptimalStepSizeForNodes(step, nodeCount);
        log.info("Using optimized step size: {}", optimizedStep);

        // Calculate number of batches
        int totalBatches = (nodeCount + optimizedStep - 1) / optimizedStep;
        CountDownLatch countDownLatch = new CountDownLatch(totalBatches);

        try {
            // Process data in optimized batches
            processNodeIdBatchesOptimized(nodeCount, optimizedStep, area, country, countDownLatch);

            // Wait for all processing to complete
            countDownLatch.await();

        } catch (Exception e) {
            log.error("Error in optimized node ID handling", e);
            throw e;
        } finally {
            // Suggest garbage collection
            System.gc();
        }

        log.info("Optimized node ID handling completed. Cost time is {}s, country is {}, area is {}",
                timer.intervalSecond(), country, area);
        return ResponseResult.OK(true, true);
    }

    @ApiOperation("map node diff column to node_rp optimized - NEW OPTIMIZED VERSION")
    @GetMapping("convert2rp-optimized")
    public ResponseResult<Boolean> convert2rpOptimized(@RequestParam(value = "step", required = false, defaultValue = "0") int step,
                                                      @RequestParam(value = "area", required = false, defaultValue = "") String area,
                                                      @RequestParam(value = "country", required = false, defaultValue = "") String country) throws InterruptedException {

        log.info("Starting optimized convert2rp for area: {}, country: {}", area, country);
        TimeInterval timer = DateUtil.timer();

        try {
            // Configure database context
            configureDatabaseContextForRead(area, country);

            // Get total record count for optimization calculations
            int totalRecords = nodeMService.count();
            log.info("Total NodeM records to be processed: {}", totalRecords);

            // Calculate optimal step size if not provided
            int optimizedStep = calculateOptimalStepSizeForConvert2rp(step, totalRecords);
            log.info("Using optimized step size: {} (original: {})", optimizedStep, step);

            // Process data in optimized batches
            processNodeConvert2rpInOptimizedBatches(area, country, totalRecords, optimizedStep);

            log.info("Optimized convert2rp completed, total cost time: {}s", timer.intervalSecond());

        } catch (Exception e) {
            log.error("Error in optimized convert2rp process for area: {}, country: {}", area, country, e);
            return ResponseResult.OK(false, false);
        } finally {
            // Explicit cleanup to help garbage collection
            cleanupConvert2rpOptimizedResources();
        }

        return ResponseResult.OK(true, true);
    }

    @GetMapping("/updateNodeLight")
    public ResponseResult<Boolean> updateNodeLight(String country,String area) throws InterruptedException {
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
        TimeInterval timer = DateUtil.timer();
        // calculate loop times
        int linkCount = nodeService.count();
        int step =32767/ BeanUtil.beanToMap(new Node()).keySet().size();
        int loop = linkCount % step != 0 ? (linkCount / step) + 1 : linkCount / step;
        CountDownLatch countDownLatch = new CountDownLatch(loop);
        for (int i = 0; i < loop; i++) {
            if (!country.isEmpty()) {
                DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
            }
            if (!area.isEmpty()) {
                MybatisPlusConfig.myTableName.set("_" + area);
            } else {
                MybatisPlusConfig.myTableName.set("");
            }
            List<Node> nodeList = nodeService.lambdaQuery()
                    .orderByDesc(Node::getHllNodeid).last("limit " + step + " offset " + i * step).list();
            nodeService.updateNodeLight(area, country, countDownLatch, nodeList);
        }
        countDownLatch.await();
        log.info("update node light completed, cost time is {}s", timer.intervalSecond());
        return ResponseResult.OK(true, true);
    }

    /**
     * Calculate optimal step size for convert2rp processing based on data volume and memory
     */
    private int calculateOptimalStepSizeForConvert2rp(int requestedStep, int totalRecords) {
        if (requestedStep > 0) {
            return requestedStep; // Use provided step if specified
        }

        // Calculate based on available memory and record count
        Runtime runtime = Runtime.getRuntime();
        long availableMemoryMB = (runtime.maxMemory() - runtime.totalMemory() + runtime.freeMemory()) / (1024 * 1024);

        int optimizedStep;
        if (totalRecords > 100000) {
            // Large dataset: use smaller batches for better memory management
            optimizedStep = Math.max(1000, Math.min(3000, (int) (availableMemoryMB / 8)));
        } else if (totalRecords > 10000) {
            // Medium dataset: moderate batches
            optimizedStep = Math.max(500, Math.min(1500, (int) (availableMemoryMB / 4)));
        } else if (totalRecords > 1000) {
            // Small dataset: larger batches for efficiency
            optimizedStep = Math.max(200, Math.min(800, totalRecords / 3));
        } else {
            // Very small dataset: process in fewer batches
            optimizedStep = Math.max(50, totalRecords / 2);
        }

        log.info("Calculated optimized step size: {} for {} records with {}MB available memory",
                optimizedStep, totalRecords, availableMemoryMB);
        return optimizedStep;
    }

    /**
     * Calculate optimal step size for node processing based on data volume
     */
    private int calculateOptimalStepSizeForNodes(int requestedStep, int totalRecords) {
        if (requestedStep > 0) {
            return requestedStep; // Use provided step if specified
        }

        // Adaptive step sizing based on data volume for node processing
        if (totalRecords <= 5000) {
            return Math.max(500, totalRecords / 5); // Small datasets: larger batches
        } else if (totalRecords <= 50000) {
            return 1400; // Medium datasets: use default size
        } else if (totalRecords <= 200000) {
            return 2000; // Large datasets: moderate batches
        } else {
            return 2500; // Very large datasets: larger batches for efficiency
        }
    }

    /**
     * Process node ID batches with optimized memory management
     */
    private void processNodeIdBatchesOptimized(int totalRecords, int optimizedStep,
                                             String area, String country,
                                             CountDownLatch countDownLatch) {

        int totalBatches = (totalRecords + optimizedStep - 1) / optimizedStep;

        log.info("Processing {} total batches for node ID handling", totalBatches);

        for (int batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
            final int currentBatch = batchIndex;
            final int offset = batchIndex * optimizedStep;

            log.info("Batch {}/{}: Processing with offset {} (step size: {})",
                    currentBatch + 1, totalBatches, offset, optimizedStep);

            try {
                // Configure database context for this batch
                if (!country.isEmpty()) {
                    DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
                }
                if (!area.isEmpty()) {
                    MybatisPlusConfig.myTableName.set("_" + area);
                } else {
                    MybatisPlusConfig.myTableName.set("");
                }

                // Fetch node data for this batch
                List<NodeM> nodeList = nodeMService.lambdaQuery()
                        .isNotNull(NodeM::getMainnodeid)
                        .or().isNotNull(NodeM::getSubnodeid)
                        .or().isNotNull(NodeM::getSubnodeid2)
                        .orderByDesc(NodeM::getHllNodeid)
                        .last("limit " + optimizedStep + " offset " + offset)
                        .list();

                log.info("Batch {}/{}: Processing {} nodes (offset: {})",
                        currentBatch + 1, totalBatches, nodeList.size(), offset);

                if (!nodeList.isEmpty()) {
                    // Use optimized service for better performance and memory management
                    optimizedNodeMService.optimizedHandleId(area, country, countDownLatch, nodeList);
                } else {
                    // No data in this batch, count down the latch
                    countDownLatch.countDown();
                }

            } catch (Exception e) {
                log.error("Error processing node batch {}/{}", currentBatch + 1, totalBatches, e);
                countDownLatch.countDown(); // Ensure latch is decremented on error
            } finally {
                // Suggest garbage collection periodically
                if (currentBatch % 10 == 0 && currentBatch > 0) {
                    log.info("Suggesting garbage collection after batch {}", currentBatch + 1);
                    System.gc();
                }
            }
        }
    }

    /**
     * Process NodeM data in optimized batches with proper memory management
     */
    private void processNodeConvert2rpInOptimizedBatches(String area, String country, int totalRecords, int optimizedStep) throws InterruptedException {

        // Process in chunks to avoid loading all data into memory at once
        int totalBatches = (totalRecords + optimizedStep - 1) / optimizedStep;

        log.info("Processing {} total batches for convert2rp", totalBatches);

        // Use optimized thread pool configuration
        CountDownLatch countDownLatch = new CountDownLatch(totalBatches);

        for (int i = 0; i < totalBatches; i++) {
            int offset = i * optimizedStep;

            log.info("Processing batch {}/{} with offset: {}, step: {}", i + 1, totalBatches, offset, optimizedStep);

            // Configure database context for each batch to ensure consistency
            configureDatabaseContextForRead(area, country);

            // Query data for this batch
            List<NodeM> nodeList = nodeMService.lambdaQuery()
                    .ne(NodeM::getStatus, 1)
                    .orderByDesc(NodeM::getNodeId)
                    .last("limit " + optimizedStep + " offset " + offset)
                    .list();

            if (!nodeList.isEmpty()) {
                // Use optimized service for better performance and memory management
                nodeService.convert2rpOptimized(area, country, countDownLatch, nodeList);
            } else {
                // No data in this batch, count down the latch
                countDownLatch.countDown();
            }

            // Cleanup after every few batches to manage memory
            if (i % 3 == 0) {
                cleanupConvert2rpOptimizedResources();
                logMemoryUsageForConvert2rp("After batch " + (i + 1));
            }
        }

        countDownLatch.await();
        log.info("All convert2rp batches completed successfully");
    }

    /**
     * Configure database context for reading operations
     */
    private void configureDatabaseContextForRead(String area, String country) {
        if (!country.isEmpty()) {
            DynamicDataSourceContextHolder.push(CommonUtils.getDsbyCountry(country, false));
        }
        if (!area.isEmpty()) {
            MybatisPlusConfig.myTableName.set("_" + area);
        } else {
            MybatisPlusConfig.myTableName.set("");
        }
    }

    /**
     * Cleanup resources to help garbage collection
     */
    private void cleanupConvert2rpOptimizedResources() {
        // Suggest garbage collection
        System.gc();

        // Log memory usage after cleanup
        Runtime runtime = Runtime.getRuntime();
        long usedMemoryMB = (runtime.totalMemory() - runtime.freeMemory()) / (1024 * 1024);
        long maxMemoryMB = runtime.maxMemory() / (1024 * 1024);

        log.debug("Memory after cleanup - Used: {}MB, Max: {}MB", usedMemoryMB, maxMemoryMB);
    }

    /**
     * Log memory usage for monitoring
     */
    private void logMemoryUsageForConvert2rp(String context) {
        Runtime runtime = Runtime.getRuntime();
        long usedMemoryMB = (runtime.totalMemory() - runtime.freeMemory()) / (1024 * 1024);
        long maxMemoryMB = runtime.maxMemory() / (1024 * 1024);
        long freeMemoryMB = runtime.freeMemory() / (1024 * 1024);

        log.info("Memory usage {}: Used={}MB, Free={}MB, Max={}MB",
                context, usedMemoryMB, freeMemoryMB, maxMemoryMB);
    }
}
