server:
  port: 8092
  tomcat:
    uri-encoding: UTF-8
spring:
  application:
    name: mapdataservice
  profiles:
    active: prd
  redis:
    host: **************
    port: 30184
    password: hll_map_road@2021
    database: 9
  datasource:
#    dynamic:
#      primary: db3
#      datasource:
#        db1:
#          driver-class-name: org.postgresql.Driver
#          type: com.zaxxer.hikari.HikariDataSource
#          #type: com.alibaba.druid.pool.DruidDataSource
#          url: jdbc:postgresql://**************:15100/mapdata_audit?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8
#          username: hllmapbasedb
#          password: hllmapbasedb@2020
#        db2:
#          driver-class-name: org.postgresql.Driver
#          type: com.zaxxer.hikari.HikariDataSource
#          #type: com.alibaba.druid.pool.DruidDataSource
#          #url: jdbc:postgresql://***************:5432/mnr?useUnicode=true&characterEncoding=utf8&currentSchema=core
#          url: jdbc:postgresql://***************:5432/mnr?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8
#          username: postgres
#          password: 1q2w3e
#        db3:
#          driver-class-name: org.postgresql.Driver
#          type: com.zaxxer.hikari.HikariDataSource
#          #type: com.alibaba.druid.pool.DruidDataSource
#          #url: jdbc:postgresql://***************:5432/here_tha?useUnicode=true&characterEncoding=utf8
#          url: jdbc:postgresql://***************:5432/here_tha?reWriteBatchedInserts=true&serverTimezone=GMT%2B8
#          username: postgres
#          password: 1q2w3e
#        db4:
#          driver-class-name: org.postgresql.Driver
#          type: com.zaxxer.hikari.HikariDataSource
#          #type: com.alibaba.druid.pool.DruidDataSource
#          #url: jdbc:postgresql://***************:5432/here_tha?useUnicode=true&characterEncoding=utf8
#          url: jdbc:postgresql://***************:5432/here_pha?reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
#          username: postgres
#          password: 1q2w3e
#        db5:
#          driver-class-name: org.postgresql.Driver
#          type: com.zaxxer.hikari.HikariDataSource
#          #type: com.alibaba.druid.pool.DruidDataSource
#          #url: jdbc:postgresql://***************:5432/here_tha?useUnicode=true&characterEncoding=utf8
#          url: jdbc:postgresql://***************:5432/phl?reWriteBatchedInserts=true&serverTimezone=GMT%2B8
#          username: postgres
#          password: 1q2w3e
#        db6:
#          driver-class-name: org.postgresql.Driver
#          type: com.zaxxer.hikari.HikariDataSource
#          #type: com.alibaba.druid.pool.DruidDataSource
#          #url: jdbc:postgresql://***************:5432/here_tha?useUnicode=true&characterEncoding=utf8
#          url: jdbc:postgresql://***************:5432/foursquare?reWriteBatchedInserts=true&serverTimezone=GMT%2B8
#          username: postgres
#          password: 1q2w3e
#        db7: #AOI数据库-母库
#          driver-class-name: org.postgresql.Driver
#          type: com.zaxxer.hikari.HikariDataSource
#          #type: com.alibaba.druid.pool.DruidDataSource
#          #url: jdbc:postgresql://***************:5432/here_tha?useUnicode=true&characterEncoding=utf8
#          url: **************************************************************************************************
#          username: postgres
#          password: hll@2020
#        db8: #hll_oversea_here_road
#          driver-class-name: com.p6spy.engine.spy.P6SpyDriver
#          type: com.zaxxer.hikari.HikariDataSource
#          #type: com.alibaba.druid.pool.DruidDataSource
#          #url: jdbc:postgresql://***************:5432/here_tha?useUnicode=true&characterEncoding=utf8
#          url: jdbc:p6spy:postgresql://***************:5432/hll_oversea_h_pha?reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
#          username: postgres
#          password: 1q2w3e
#        db9: #hll_oversea_here_road
#          driver-class-name: org.postgresql.Driver
#          type: com.zaxxer.hikari.HikariDataSource
#          #url: jdbc:postgresql://***************:5432/mnr?useUnicode=true&characterEncoding=utf8&currentSchema=core
#          url: ***************************************************************************************************************************************************************************
#          username: postgres
#          password: Huolala@2021
#    pool-name: principalHikariCP
#    maximum-pool-size: 5
#    type: com.zaxxer.hikari.HikariDataSource
#    dynamic:
#      primary: db3 #设置默认的数据源或者数据源组,默认值即为master
#      datasource:
#        db1:
#          driver-class-name: org.postgresql.Driver
#          type: com.zaxxer.hikari.HikariDataSource
#          #type: com.alibaba.druid.pool.DruidDataSource
#          url: jdbc:postgresql://**************:15100/mapdata_audit?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8
#          username: hllmapbasedb
#          password: hllmapbasedb@2020
#        db2:
#          driver-class-name: org.postgresql.Driver
#          type: com.zaxxer.hikari.HikariDataSource
#          #type: com.alibaba.druid.pool.DruidDataSource
#          #url: jdbc:postgresql://***************:5432/mnr?useUnicode=true&characterEncoding=utf8&currentSchema=core
#          url: jdbc:postgresql://***************:5432/mnr?useUnicode=true&characterEncoding=utf8&reWriteBatchedInserts=true&serverTimezone=GMT%2B8
#          username: postgres
#          password: 1q2w3e
#        db3:
#          driver-class-name: org.postgresql.Driver
#          type: com.zaxxer.hikari.HikariDataSource
#          #type: com.alibaba.druid.pool.DruidDataSource
#          #url: jdbc:postgresql://***************:5432/here_tha?useUnicode=true&characterEncoding=utf8
#          url: jdbc:postgresql://***************:5432/here_tha?reWriteBatchedInserts=true&serverTimezone=GMT%2B8
#          username: postgres
#          password: 1q2w3e
#        db4:
#          driver-class-name: org.postgresql.Driver
#          type: com.zaxxer.hikari.HikariDataSource
#          #type: com.alibaba.druid.pool.DruidDataSource
#          #url: jdbc:postgresql://***************:5432/here_tha?useUnicode=true&characterEncoding=utf8
#          url: jdbc:postgresql://***************:5432/here_pha?reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
#          username: postgres
#          password: 1q2w3e
#        db5:
#          driver-class-name: org.postgresql.Driver
#          type: com.zaxxer.hikari.HikariDataSource
#          #type: com.alibaba.druid.pool.DruidDataSource
#          #url: jdbc:postgresql://***************:5432/here_tha?useUnicode=true&characterEncoding=utf8
#          url: jdbc:postgresql://***************:5432/phl?reWriteBatchedInserts=true&serverTimezone=GMT%2B8
#          username: postgres
#          password: 1q2w3e
#        db6:
#          driver-class-name: org.postgresql.Driver
#          type: com.zaxxer.hikari.HikariDataSource
#          #type: com.alibaba.druid.pool.DruidDataSource
#          #url: jdbc:postgresql://***************:5432/here_tha?useUnicode=true&characterEncoding=utf8
#          url: jdbc:postgresql://***************:5432/foursquare?reWriteBatchedInserts=true&serverTimezone=GMT%2B8
#          username: postgres
#          password: 1q2w3e
#        db8: #hll_oversea_here_road
#          driver-class-name: org.postgresql.Driver
#          type: com.zaxxer.hikari.HikariDataSource
#          #type: com.alibaba.druid.pool.DruidDataSource
#          #url: jdbc:postgresql://***************:5432/here_tha?useUnicode=true&characterEncoding=utf8
#          url: jdbc:postgresql://***************:5432/hll_oversea_h_pha?reWriteBatchedInserts=true&serverTimezone=GMT%2B8&stringtype=unspecified
#          username: postgres
#          password: 1q2w3e

third:
  aoibaseservice:
    url: "http://192.168.106.46:8089"
  roadmatchservice:
    url: "http://192.168.106.199:8080"
  hereroadmatchservice:
    url: "http://192.168.106.199:9002"
  googleNearByPoiSearch:
    url: "https://maps.googleapis.com"
  middlePlatformService:
    url: "http://map-provider-stg.myhll.sg"
  wrapperGoogleNearByPoiSearch:
    url: "http://map-facade-pre.myhll.sg"
  hereRoadRoute:
    url: "https://router.hereapi.com"
  databasebackupservice:
    url: "http://192.168.160.42:10098"

logging:
  file:
    path: ~/logs
  level:
    default: debug
    aoibaseservice: info

mybatis-plus:
  mapper-locations: classpath:xml/*.xml
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

hll:
  client:
    id:
      url: http://192.168.160.26:8995/
management:
  endpoints:
    web:
      exposure:
        include: "*"



MapTT:
  mapttconfigfile:
    path: "./common/src/main/java/com/hll/mapdataservice/common/config/mapping-tt.json"


