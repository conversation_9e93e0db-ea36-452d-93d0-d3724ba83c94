package com.hll.mapdataservice.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hll.mapdataservice.common.entity.Node;
import com.hll.mapdataservice.common.entity.NodeM;

import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * <p>
 *
 * </p>
 *
 * @Author: ares.chen
 * @Since: 2021/8/17
 */
public interface INodeService extends IService<Node> {
    /**
     * convert node to node_rp
     */
    void convert2rp(String area,String country, CountDownLatch countDownLatch,List<NodeM> nodeList);

    /**
     * convert node to node_rp optimized version
     */
    void convert2rpOptimized(String area, String country, CountDownLatch countDownLatch, List<NodeM> nodeList);

    void handleId(String area, String country, CountDownLatch countDownLatch, List<Node> nodeList);

    void updateNodeLight(String area, String country, CountDownLatch countDownLatch, List<Node> nodeList);

}
