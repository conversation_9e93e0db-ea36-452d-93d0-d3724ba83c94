package com.hll.mapdataservice.common.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.hll.mapdataservice.common.entity.Zlevels;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-23
 */
//@DS("db4")
//@DS("${datasourceinfo.datasource}")
public interface ZlevelsMapper extends BaseMapper<Zlevels> {

    @Select("SELECT array_agg(gid) AS gid_values " +
            "FROM zlevels " +
            "GROUP BY geom " +
            "HAVING COUNT(*) > 1 AND COUNT(DISTINCT z_level) > 1 " +
            "ORDER BY COUNT(*) DESC")
    List<Map<String, Object>> findDuplicateGeomWithDifferentZLevels();

}
