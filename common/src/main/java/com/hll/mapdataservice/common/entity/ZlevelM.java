package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@ApiModel(value="ZlevelM对象", description="")
public class ZlevelM extends Model<ZlevelM> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "zlevel_id", type = IdType.AUTO)
    private String zlevelId;

    private String hllLinkid;

    private Integer pointNum;

    private String hllNodeid;

    private String geom;

    private String z;

    private String linktype;

    private String intrsect;

    private Integer dotShape;

    private String aligned;

    private String levelId;

    private String tileId;

    private Integer tileType;

    private String datasource;

    private LocalDateTime upDate;

    private Integer status;

    public String getZlevelId() {
        return zlevelId;
    }

    public void setZlevelId(String zlevelId) {
        this.zlevelId = zlevelId;
    }
    public String getHllLinkid() {
        return hllLinkid;
    }

    public void setHllLinkid(String hllLinkid) {
        this.hllLinkid = hllLinkid;
    }
    public Integer getPointNum() {
        return pointNum;
    }

    public void setPointNum(Integer pointNum) {
        this.pointNum = pointNum;
    }
    public String getHllNodeid() {
        return hllNodeid;
    }

    public void setHllNodeid(String hllNodeid) {
        this.hllNodeid = hllNodeid;
    }
    public String getGeom() {
        return geom;
    }

    public void setGeom(String geom) {
        this.geom = geom;
    }
    public String getZ() {
        return z;
    }

    public void setZ(String z) {
        this.z = z;
    }
    public String getLinktype() {
        return linktype;
    }

    public void setLinktype(String linktype) {
        this.linktype = linktype;
    }
    public String getIntrsect() {
        return intrsect;
    }

    public void setIntrsect(String intrsect) {
        this.intrsect = intrsect;
    }
    public Integer getDotShape() {
        return dotShape;
    }

    public void setDotShape(Integer dotShape) {
        this.dotShape = dotShape;
    }
    public String getAligned() {
        return aligned;
    }

    public void setAligned(String aligned) {
        this.aligned = aligned;
    }
    public String getLevelId() {
        return levelId;
    }

    public void setLevelId(String levelId) {
        this.levelId = levelId;
    }
    public String getTileId() {
        return tileId;
    }

    public void setTileId(String tileId) {
        this.tileId = tileId;
    }
    public Integer getTileType() {
        return tileType;
    }

    public void setTileType(Integer tileType) {
        this.tileType = tileType;
    }
    public String getDatasource() {
        return datasource;
    }

    public void setDatasource(String datasource) {
        this.datasource = datasource;
    }
    public LocalDateTime getUpDate() {
        return upDate;
    }

    public void setUpDate(LocalDateTime upDate) {
        this.upDate = upDate;
    }
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    protected Serializable pkVal() {
        return this.zlevelId;
    }

    @Override
    public String toString() {
        return "Zlevel{" +
            "zlevelId=" + zlevelId +
            ", hllLinkid=" + hllLinkid +
            ", pointNum=" + pointNum +
            ", hllNodeid=" + hllNodeid +
            ", geom=" + geom +
            ", z=" + z +
            ", linktype=" + linktype +
            ", intrsect=" + intrsect +
            ", dotShape=" + dotShape +
            ", aligned=" + aligned +
            ", levelId=" + levelId +
            ", tileId=" + tileId +
            ", tileType=" + tileType +
            ", datasource=" + datasource +
            ", upDate=" + upDate +
            ", status=" + status +
        "}";
    }
}
