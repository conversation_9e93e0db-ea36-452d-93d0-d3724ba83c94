package com.hll.mapdataservice.common.entity;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.postgis.Geometry;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-12
 */
@ApiModel(value="HerePhaStreets对象", description="\"")
//@DS("db5")
@TableName(value = "streets")
public class HerePhaStreets extends Model<HerePhaStreets> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "gid", type = IdType.AUTO)
    private Integer gid;

    private Integer linkId;

    private String stName;

    private Integer featId;

    private String stLangcd;

    private Integer numStnmes;

    private String stNmPref;

    private String stTypBef;

    private String stNmBase;

    private String stNmSuff;

    private String stTypAft;

    private String stTypAtt;

    private String addrType;

    private String lRefaddr;

    private String lNrefaddr;

    private String lAddrsch;

    private String lAddrform;

    private String rRefaddr;

    private String rNrefaddr;

    private String rAddrsch;

    private String rAddrform;

    private Integer refInId;

    private Integer nrefInId;

    private Integer nShapepnt;

    private String funcClass;

    private String speedCat;

    private Integer frSpdLim;

    private Integer toSpdLim;

    private Integer toLanes;

    private Integer fromLanes;

    private String enhGeom;

    private String laneCat;

    private String divider;

    private String dirTravel;

    private Integer lAreaId;

    private Integer rAreaId;

    private String lPostcode;

    private String rPostcode;

    private Integer lNumzones;

    private Integer rNumzones;

    private Integer numAdRng;

    private String arAuto;

    private String arBus;

    private String arTaxis;

    private String arCarpool;

    private String arPedest;

    private String arTrucks;

    private String arTraff;

    private String arDeliv;

    private String arEmerveh;

    private String arMotor;

    private String paved;

    @TableField(value = "private")
    private String privateinfo;

    private String frontage;

    private String bridge;

    private String tunnel;

    private String ramp;

    private String tollway;

    private String poiaccess;

    private String contracc;

    private String roundabout;

    private String interinter;

    private String undeftraff;

    private String ferryType;

    private String multidigit;

    private String maxattr;

    private String spectrfig;

    private String indescrib;

    private String manoeuvre;

    private String dividerleg;

    private String inprocdata;

    private String fullGeom;

    private String urban;

    private String routeType;

    private String dironsign;

    private String explicatbl;

    private String nameonrdsn;

    private String postalname;

    private String stalename;

    private String vanityname;

    private String junctionnm;

    private String exitname;

    private String scenicRt;

    private String scenicNm;

    private String fourwhldr;

    private String coverind;

    private String plotRoad;

    private String reversible;

    private String exprLane;

    private String carpoolrd;

    private Integer physLanes;

    private String verTrans;

    private String pubAccess;

    private String lowMblty;

    private String priorityrd;

    private String spdLmSrc;

    private String expandInc;

    private String transArea;

    @TableField("ST_AsText(\"geom\")")
    private String geometry;

    public Double getLen() {
        return len;
    }

    public void setLen(Double len) {
        this.len = len;
    }

    @TableField(value = "st_length(\"geom\",true)")
    private Double len;

    public String getGeom() {
        return geom;
    }

    public void setGeom(String geom) {
        this.geom = geom;
    }

    @TableField(value = "\"geom\"")
    private String geom;

    public Integer getGid() {
        return gid;
    }

    public void setGid(Integer gid) {
        this.gid = gid;
    }
    public Integer getLinkId() {
        return linkId;
    }

    public void setLinkId(Integer linkId) {
        this.linkId = linkId;
    }
    public String getStName() {
        return stName;
    }

    public void setStName(String stName) {
        this.stName = stName;
    }
    public Integer getFeatId() {
        return featId;
    }

    public void setFeatId(Integer featId) {
        this.featId = featId;
    }
    public String getStLangcd() {
        return stLangcd;
    }

    public void setStLangcd(String stLangcd) {
        this.stLangcd = stLangcd;
    }
    public Integer getNumStnmes() {
        return numStnmes;
    }

    public void setNumStnmes(Integer numStnmes) {
        this.numStnmes = numStnmes;
    }
    public String getStNmPref() {
        return stNmPref;
    }

    public void setStNmPref(String stNmPref) {
        this.stNmPref = stNmPref;
    }
    public String getStTypBef() {
        return stTypBef;
    }

    public void setStTypBef(String stTypBef) {
        this.stTypBef = stTypBef;
    }
    public String getStNmBase() {
        return stNmBase;
    }

    public void setStNmBase(String stNmBase) {
        this.stNmBase = stNmBase;
    }
    public String getStNmSuff() {
        return stNmSuff;
    }

    public void setStNmSuff(String stNmSuff) {
        this.stNmSuff = stNmSuff;
    }
    public String getStTypAft() {
        return stTypAft;
    }

    public void setStTypAft(String stTypAft) {
        this.stTypAft = stTypAft;
    }
    public String getStTypAtt() {
        return stTypAtt;
    }

    public void setStTypAtt(String stTypAtt) {
        this.stTypAtt = stTypAtt;
    }
    public String getAddrType() {
        return addrType;
    }

    public void setAddrType(String addrType) {
        this.addrType = addrType;
    }
    public String getlRefaddr() {
        return lRefaddr;
    }

    public void setlRefaddr(String lRefaddr) {
        this.lRefaddr = lRefaddr;
    }
    public String getlNrefaddr() {
        return lNrefaddr;
    }

    public void setlNrefaddr(String lNrefaddr) {
        this.lNrefaddr = lNrefaddr;
    }
    public String getlAddrsch() {
        return lAddrsch;
    }

    public void setlAddrsch(String lAddrsch) {
        this.lAddrsch = lAddrsch;
    }
    public String getlAddrform() {
        return lAddrform;
    }

    public void setlAddrform(String lAddrform) {
        this.lAddrform = lAddrform;
    }
    public String getrRefaddr() {
        return rRefaddr;
    }

    public void setrRefaddr(String rRefaddr) {
        this.rRefaddr = rRefaddr;
    }
    public String getrNrefaddr() {
        return rNrefaddr;
    }

    public void setrNrefaddr(String rNrefaddr) {
        this.rNrefaddr = rNrefaddr;
    }
    public String getrAddrsch() {
        return rAddrsch;
    }

    public void setrAddrsch(String rAddrsch) {
        this.rAddrsch = rAddrsch;
    }
    public String getrAddrform() {
        return rAddrform;
    }

    public void setrAddrform(String rAddrform) {
        this.rAddrform = rAddrform;
    }
    public Integer getRefInId() {
        return refInId;
    }

    public void setRefInId(Integer refInId) {
        this.refInId = refInId;
    }
    public Integer getNrefInId() {
        return nrefInId;
    }

    public void setNrefInId(Integer nrefInId) {
        this.nrefInId = nrefInId;
    }
    public Integer getnShapepnt() {
        return nShapepnt;
    }

    public void setnShapepnt(Integer nShapepnt) {
        this.nShapepnt = nShapepnt;
    }
    public String getFuncClass() {
        return funcClass;
    }

    public void setFuncClass(String funcClass) {
        this.funcClass = funcClass;
    }
    public String getSpeedCat() {
        return speedCat;
    }

    public void setSpeedCat(String speedCat) {
        this.speedCat = speedCat;
    }
    public Integer getFrSpdLim() {
        return frSpdLim;
    }

    public void setFrSpdLim(Integer frSpdLim) {
        this.frSpdLim = frSpdLim;
    }
    public Integer getToSpdLim() {
        return toSpdLim;
    }

    public void setToSpdLim(Integer toSpdLim) {
        this.toSpdLim = toSpdLim;
    }
    public Integer getToLanes() {
        return toLanes;
    }

    public void setToLanes(Integer toLanes) {
        this.toLanes = toLanes;
    }
    public Integer getFromLanes() {
        return fromLanes;
    }

    public void setFromLanes(Integer fromLanes) {
        this.fromLanes = fromLanes;
    }
    public String getEnhGeom() {
        return enhGeom;
    }

    public void setEnhGeom(String enhGeom) {
        this.enhGeom = enhGeom;
    }
    public String getLaneCat() {
        return laneCat;
    }

    public void setLaneCat(String laneCat) {
        this.laneCat = laneCat;
    }
    public String getDivider() {
        return divider;
    }

    public void setDivider(String divider) {
        this.divider = divider;
    }
    public String getDirTravel() {
        return dirTravel;
    }

    public void setDirTravel(String dirTravel) {
        this.dirTravel = dirTravel;
    }
    public Integer getlAreaId() {
        return lAreaId;
    }

    public void setlAreaId(Integer lAreaId) {
        this.lAreaId = lAreaId;
    }
    public Integer getrAreaId() {
        return rAreaId;
    }

    public void setrAreaId(Integer rAreaId) {
        this.rAreaId = rAreaId;
    }
    public String getlPostcode() {
        return lPostcode;
    }

    public void setlPostcode(String lPostcode) {
        this.lPostcode = lPostcode;
    }
    public String getrPostcode() {
        return rPostcode;
    }

    public void setrPostcode(String rPostcode) {
        this.rPostcode = rPostcode;
    }
    public Integer getlNumzones() {
        return lNumzones;
    }

    public void setlNumzones(Integer lNumzones) {
        this.lNumzones = lNumzones;
    }
    public Integer getrNumzones() {
        return rNumzones;
    }

    public void setrNumzones(Integer rNumzones) {
        this.rNumzones = rNumzones;
    }
    public Integer getNumAdRng() {
        return numAdRng;
    }

    public void setNumAdRng(Integer numAdRng) {
        this.numAdRng = numAdRng;
    }
    public String getArAuto() {
        return arAuto;
    }

    public void setArAuto(String arAuto) {
        this.arAuto = arAuto;
    }
    public String getArBus() {
        return arBus;
    }

    public void setArBus(String arBus) {
        this.arBus = arBus;
    }
    public String getArTaxis() {
        return arTaxis;
    }

    public void setArTaxis(String arTaxis) {
        this.arTaxis = arTaxis;
    }
    public String getArCarpool() {
        return arCarpool;
    }

    public void setArCarpool(String arCarpool) {
        this.arCarpool = arCarpool;
    }
    public String getArPedest() {
        return arPedest;
    }

    public void setArPedest(String arPedest) {
        this.arPedest = arPedest;
    }
    public String getArTrucks() {
        return arTrucks;
    }

    public void setArTrucks(String arTrucks) {
        this.arTrucks = arTrucks;
    }
    public String getArTraff() {
        return arTraff;
    }

    public void setArTraff(String arTraff) {
        this.arTraff = arTraff;
    }
    public String getArDeliv() {
        return arDeliv;
    }

    public void setArDeliv(String arDeliv) {
        this.arDeliv = arDeliv;
    }
    public String getArEmerveh() {
        return arEmerveh;
    }

    public void setArEmerveh(String arEmerveh) {
        this.arEmerveh = arEmerveh;
    }
    public String getArMotor() {
        return arMotor;
    }

    public void setArMotor(String arMotor) {
        this.arMotor = arMotor;
    }
    public String getPaved() {
        return paved;
    }

    public void setPaved(String paved) {
        this.paved = paved;
    }
    public String getPrivateinfo() {
        return privateinfo;
    }

    public void setPrivateinfo(String privateinfo) {
        this.privateinfo = privateinfo;
    }
    public String getFrontage() {
        return frontage;
    }

    public void setFrontage(String frontage) {
        this.frontage = frontage;
    }
    public String getBridge() {
        return bridge;
    }

    public void setBridge(String bridge) {
        this.bridge = bridge;
    }
    public String getTunnel() {
        return tunnel;
    }

    public void setTunnel(String tunnel) {
        this.tunnel = tunnel;
    }
    public String getRamp() {
        return ramp;
    }

    public void setRamp(String ramp) {
        this.ramp = ramp;
    }
    public String getTollway() {
        return tollway;
    }

    public void setTollway(String tollway) {
        this.tollway = tollway;
    }
    public String getPoiaccess() {
        return poiaccess;
    }

    public void setPoiaccess(String poiaccess) {
        this.poiaccess = poiaccess;
    }
    public String getContracc() {
        return contracc;
    }

    public void setContracc(String contracc) {
        this.contracc = contracc;
    }
    public String getRoundabout() {
        return roundabout;
    }

    public void setRoundabout(String roundabout) {
        this.roundabout = roundabout;
    }
    public String getInterinter() {
        return interinter;
    }

    public void setInterinter(String interinter) {
        this.interinter = interinter;
    }
    public String getUndeftraff() {
        return undeftraff;
    }

    public void setUndeftraff(String undeftraff) {
        this.undeftraff = undeftraff;
    }
    public String getFerryType() {
        return ferryType;
    }

    public void setFerryType(String ferryType) {
        this.ferryType = ferryType;
    }
    public String getMultidigit() {
        return multidigit;
    }

    public void setMultidigit(String multidigit) {
        this.multidigit = multidigit;
    }
    public String getMaxattr() {
        return maxattr;
    }

    public void setMaxattr(String maxattr) {
        this.maxattr = maxattr;
    }
    public String getSpectrfig() {
        return spectrfig;
    }

    public void setSpectrfig(String spectrfig) {
        this.spectrfig = spectrfig;
    }
    public String getIndescrib() {
        return indescrib;
    }

    public void setIndescrib(String indescrib) {
        this.indescrib = indescrib;
    }
    public String getManoeuvre() {
        return manoeuvre;
    }

    public void setManoeuvre(String manoeuvre) {
        this.manoeuvre = manoeuvre;
    }
    public String getDividerleg() {
        return dividerleg;
    }

    public void setDividerleg(String dividerleg) {
        this.dividerleg = dividerleg;
    }
    public String getInprocdata() {
        return inprocdata;
    }

    public void setInprocdata(String inprocdata) {
        this.inprocdata = inprocdata;
    }
    public String getFullGeom() {
        return fullGeom;
    }

    public void setFullGeom(String fullGeom) {
        this.fullGeom = fullGeom;
    }
    public String getUrban() {
        return urban;
    }

    public void setUrban(String urban) {
        this.urban = urban;
    }
    public String getRouteType() {
        return routeType;
    }

    public void setRouteType(String routeType) {
        this.routeType = routeType;
    }
    public String getDironsign() {
        return dironsign;
    }

    public void setDironsign(String dironsign) {
        this.dironsign = dironsign;
    }
    public String getExplicatbl() {
        return explicatbl;
    }

    public void setExplicatbl(String explicatbl) {
        this.explicatbl = explicatbl;
    }
    public String getNameonrdsn() {
        return nameonrdsn;
    }

    public void setNameonrdsn(String nameonrdsn) {
        this.nameonrdsn = nameonrdsn;
    }
    public String getPostalname() {
        return postalname;
    }

    public void setPostalname(String postalname) {
        this.postalname = postalname;
    }
    public String getStalename() {
        return stalename;
    }

    public void setStalename(String stalename) {
        this.stalename = stalename;
    }
    public String getVanityname() {
        return vanityname;
    }

    public void setVanityname(String vanityname) {
        this.vanityname = vanityname;
    }
    public String getJunctionnm() {
        return junctionnm;
    }

    public void setJunctionnm(String junctionnm) {
        this.junctionnm = junctionnm;
    }
    public String getExitname() {
        return exitname;
    }

    public void setExitname(String exitname) {
        this.exitname = exitname;
    }
    public String getScenicRt() {
        return scenicRt;
    }

    public void setScenicRt(String scenicRt) {
        this.scenicRt = scenicRt;
    }
    public String getScenicNm() {
        return scenicNm;
    }

    public void setScenicNm(String scenicNm) {
        this.scenicNm = scenicNm;
    }
    public String getFourwhldr() {
        return fourwhldr;
    }

    public void setFourwhldr(String fourwhldr) {
        this.fourwhldr = fourwhldr;
    }
    public String getCoverind() {
        return coverind;
    }

    public void setCoverind(String coverind) {
        this.coverind = coverind;
    }
    public String getPlotRoad() {
        return plotRoad;
    }

    public void setPlotRoad(String plotRoad) {
        this.plotRoad = plotRoad;
    }
    public String getReversible() {
        return reversible;
    }

    public void setReversible(String reversible) {
        this.reversible = reversible;
    }
    public String getExprLane() {
        return exprLane;
    }

    public void setExprLane(String exprLane) {
        this.exprLane = exprLane;
    }
    public String getCarpoolrd() {
        return carpoolrd;
    }

    public void setCarpoolrd(String carpoolrd) {
        this.carpoolrd = carpoolrd;
    }
    public Integer getPhysLanes() {
        return physLanes;
    }

    public void setPhysLanes(Integer physLanes) {
        this.physLanes = physLanes;
    }
    public String getVerTrans() {
        return verTrans;
    }

    public void setVerTrans(String verTrans) {
        this.verTrans = verTrans;
    }
    public String getPubAccess() {
        return pubAccess;
    }

    public void setPubAccess(String pubAccess) {
        this.pubAccess = pubAccess;
    }
    public String getLowMblty() {
        return lowMblty;
    }

    public void setLowMblty(String lowMblty) {
        this.lowMblty = lowMblty;
    }
    public String getPriorityrd() {
        return priorityrd;
    }

    public void setPriorityrd(String priorityrd) {
        this.priorityrd = priorityrd;
    }
    public String getSpdLmSrc() {
        return spdLmSrc;
    }

    public void setSpdLmSrc(String spdLmSrc) {
        this.spdLmSrc = spdLmSrc;
    }
    public String getExpandInc() {
        return expandInc;
    }

    public void setExpandInc(String expandInc) {
        this.expandInc = expandInc;
    }
    public String getTransArea() {
        return transArea;
    }

    public void setTransArea(String transArea) {
        this.transArea = transArea;
    }
    public String getGeometry() {
        return geometry;
    }

    public void setGeometry(String geometry) {
        this.geometry = geometry;
    }

    @Override
    protected Serializable pkVal() {
        return this.gid;
    }

    @Override
    public String toString() {
        return "HerePhaStreets{" +
                "index='" + gid + '\'' +
                ", linkId=" + linkId +
                ", stName='" + stName + '\'' +
                ", featId='" + featId + '\'' +
                ", stLangcd='" + stLangcd + '\'' +
                ", numStnmes='" + numStnmes + '\'' +
                ", stNmPref='" + stNmPref + '\'' +
                ", stTypBef='" + stTypBef + '\'' +
                ", stNmBase='" + stNmBase + '\'' +
                ", stNmSuff='" + stNmSuff + '\'' +
                ", stTypAft='" + stTypAft + '\'' +
                ", stTypAtt='" + stTypAtt + '\'' +
                ", addrType='" + addrType + '\'' +
                ", lRefaddr='" + lRefaddr + '\'' +
                ", lNrefaddr='" + lNrefaddr + '\'' +
                ", lAddrsch='" + lAddrsch + '\'' +
                ", lAddrform='" + lAddrform + '\'' +
                ", rRefaddr='" + rRefaddr + '\'' +
                ", rNrefaddr='" + rNrefaddr + '\'' +
                ", rAddrsch='" + rAddrsch + '\'' +
                ", rAddrform='" + rAddrform + '\'' +
                ", refInId='" + refInId + '\'' +
                ", nrefInId='" + nrefInId + '\'' +
                ", nShapepnt='" + nShapepnt + '\'' +
                ", funcClass='" + funcClass + '\'' +
                ", speedCat='" + speedCat + '\'' +
                ", frSpdLim='" + frSpdLim + '\'' +
                ", toSpdLim='" + toSpdLim + '\'' +
                ", toLanes='" + toLanes + '\'' +
                ", fromLanes='" + fromLanes + '\'' +
                ", enhGeom='" + enhGeom + '\'' +
                ", laneCat='" + laneCat + '\'' +
                ", divider='" + divider + '\'' +
                ", dirTravel='" + dirTravel + '\'' +
                ", lAreaId='" + lAreaId + '\'' +
                ", rAreaId='" + rAreaId + '\'' +
                ", lPostcode='" + lPostcode + '\'' +
                ", rPostcode='" + rPostcode + '\'' +
                ", lNumzones='" + lNumzones + '\'' +
                ", rNumzones='" + rNumzones + '\'' +
                ", numAdRng='" + numAdRng + '\'' +
                ", arAuto='" + arAuto + '\'' +
                ", arBus='" + arBus + '\'' +
                ", arTaxis='" + arTaxis + '\'' +
                ", arCarpool='" + arCarpool + '\'' +
                ", arPedest='" + arPedest + '\'' +
                ", arTrucks='" + arTrucks + '\'' +
                ", arTraff='" + arTraff + '\'' +
                ", arDeliv='" + arDeliv + '\'' +
                ", arEmerveh='" + arEmerveh + '\'' +
                ", arMotor='" + arMotor + '\'' +
                ", paved='" + paved + '\'' +
                ", privateinfo='" + privateinfo + '\'' +
                ", frontage='" + frontage + '\'' +
                ", bridge='" + bridge + '\'' +
                ", tunnel='" + tunnel + '\'' +
                ", ramp='" + ramp + '\'' +
                ", tollway='" + tollway + '\'' +
                ", poiaccess='" + poiaccess + '\'' +
                ", contracc='" + contracc + '\'' +
                ", roundabout='" + roundabout + '\'' +
                ", interinter='" + interinter + '\'' +
                ", undeftraff='" + undeftraff + '\'' +
                ", ferryType='" + ferryType + '\'' +
                ", multidigit='" + multidigit + '\'' +
                ", maxattr='" + maxattr + '\'' +
                ", spectrfig='" + spectrfig + '\'' +
                ", indescrib='" + indescrib + '\'' +
                ", manoeuvre='" + manoeuvre + '\'' +
                ", dividerleg='" + dividerleg + '\'' +
                ", inprocdata='" + inprocdata + '\'' +
                ", fullGeom='" + fullGeom + '\'' +
                ", urban='" + urban + '\'' +
                ", routeType='" + routeType + '\'' +
                ", dironsign='" + dironsign + '\'' +
                ", explicatbl='" + explicatbl + '\'' +
                ", nameonrdsn='" + nameonrdsn + '\'' +
                ", postalname='" + postalname + '\'' +
                ", stalename='" + stalename + '\'' +
                ", vanityname='" + vanityname + '\'' +
                ", junctionnm='" + junctionnm + '\'' +
                ", exitname='" + exitname + '\'' +
                ", scenicRt='" + scenicRt + '\'' +
                ", scenicNm='" + scenicNm + '\'' +
                ", fourwhldr='" + fourwhldr + '\'' +
                ", coverind='" + coverind + '\'' +
                ", plotRoad='" + plotRoad + '\'' +
                ", reversible='" + reversible + '\'' +
                ", exprLane='" + exprLane + '\'' +
                ", carpoolrd='" + carpoolrd + '\'' +
                ", physLanes='" + physLanes + '\'' +
                ", verTrans='" + verTrans + '\'' +
                ", pubAccess='" + pubAccess + '\'' +
                ", lowMblty='" + lowMblty + '\'' +
                ", priorityrd='" + priorityrd + '\'' +
                ", spdLmSrc='" + spdLmSrc + '\'' +
                ", expandInc='" + expandInc + '\'' +
                ", transArea='" + transArea + '\'' +
                ", geometry='" + geometry + '\'' +
                ", len=" + len +
                ", geom='" + geom + '\'' +
                '}';
    }
}
