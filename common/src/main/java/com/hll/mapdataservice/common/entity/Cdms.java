package com.hll.mapdataservice.common.entity;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-21
 */
@ApiModel(value="Cdms对象", description="")
@TableName("cdms")
public class Cdms extends Model<Cdms> {

    private static final long serialVersionUID = 1L;

    @TableId
    private Integer gid;
    private Integer linkId;

    private Integer condId;

    private Integer condType;

    private String condVal1;

    private String condVal2;

    private String condVal3;

    private Integer condVal4;

    private String endOfLk;

    private String arAuto;

    private String arBus;

    private String arTaxis;

    private String arCarpool;

    private String arPedstrn;

    private String arTrucks;

    private String arThrutr;

    private String arDeliver;

    private String arEmerveh;

    private String arMotor;

    public Integer getLinkId() {
        return linkId;
    }

    public void setLinkId(Integer linkId) {
        this.linkId = linkId;
    }
    public Integer getCondId() {
        return condId;
    }

    public void setCondId(Integer condId) {
        this.condId = condId;
    }
    public Integer getCondType() {
        return condType;
    }

    public void setCondType(Integer condType) {
        this.condType = condType;
    }
    public String getCondVal1() {
        return condVal1;
    }

    public void setCondVal1(String condVal1) {
        this.condVal1 = condVal1;
    }
    public String getCondVal2() {
        return condVal2;
    }

    public void setCondVal2(String condVal2) {
        this.condVal2 = condVal2;
    }
    public String getCondVal3() {
        return condVal3;
    }

    public void setCondVal3(String condVal3) {
        this.condVal3 = condVal3;
    }
    public Integer getCondVal4() {
        return condVal4;
    }

    public void setCondVal4(Integer condVal4) {
        this.condVal4 = condVal4;
    }
    public String getEndOfLk() {
        return endOfLk;
    }

    public void setEndOfLk(String endOfLk) {
        this.endOfLk = endOfLk;
    }
    public String getArAuto() {
        return arAuto;
    }

    public void setArAuto(String arAuto) {
        this.arAuto = arAuto;
    }
    public String getArBus() {
        return arBus;
    }

    public void setArBus(String arBus) {
        this.arBus = arBus;
    }
    public String getArTaxis() {
        return arTaxis;
    }

    public void setArTaxis(String arTaxis) {
        this.arTaxis = arTaxis;
    }
    public String getArCarpool() {
        return arCarpool;
    }

    public void setArCarpool(String arCarpool) {
        this.arCarpool = arCarpool;
    }
    public String getArPedstrn() {
        return arPedstrn;
    }

    public void setArPedstrn(String arPedstrn) {
        this.arPedstrn = arPedstrn;
    }
    public String getArTrucks() {
        return arTrucks;
    }

    public void setArTrucks(String arTrucks) {
        this.arTrucks = arTrucks;
    }
    public String getArThrutr() {
        return arThrutr;
    }

    public void setArThrutr(String arThrutr) {
        this.arThrutr = arThrutr;
    }
    public String getArDeliver() {
        return arDeliver;
    }

    public void setArDeliver(String arDeliver) {
        this.arDeliver = arDeliver;
    }
    public String getArEmerveh() {
        return arEmerveh;
    }

    public void setArEmerveh(String arEmerveh) {
        this.arEmerveh = arEmerveh;
    }
    public String getArMotor() {
        return arMotor;
    }

    public void setArMotor(String arMotor) {
        this.arMotor = arMotor;
    }

    public Integer getGid() {
        return gid;
    }

    public void setGid(Integer gid) {
        this.gid = gid;
    }

    @Override
    protected Serializable pkVal() {
        return this.linkId;
    }

    @Override
    public String toString() {
        return "Cdms{" +
            "linkId=" + linkId +
            ", condId=" + condId +
            ", condType=" + condType +
            ", condVal1=" + condVal1 +
            ", condVal2=" + condVal2 +
            ", condVal3=" + condVal3 +
            ", condVal4=" + condVal4 +
            ", endOfLk=" + endOfLk +
            ", arAuto=" + arAuto +
            ", arBus=" + arBus +
            ", arTaxis=" + arTaxis +
            ", arCarpool=" + arCarpool +
            ", arPedstrn=" + arPedstrn +
            ", arTrucks=" + arTrucks +
            ", arThrutr=" + arThrutr +
            ", arDeliver=" + arDeliver +
            ", arEmerveh=" + arEmerveh +
            ", arMotor=" + arMotor +
        "}";
    }
}
