package com.hll.mapdataservice.common.entity;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-21
 */
@ApiModel(value="Streettrans对象", description="")
public class Streettrans extends Model<Streettrans> {

    private static final long serialVersionUID = 1L;

    private Integer featureId;

    private String transType;

    private String stNameTr;

    private String stBaseTr;

    private String trStrAft;

    private String trStrAtt;

    public Integer getFeatureId() {
        return featureId;
    }

    public void setFeatureId(Integer featureId) {
        this.featureId = featureId;
    }
    public String getTransType() {
        return transType;
    }

    public void setTransType(String transType) {
        this.transType = transType;
    }
    public String getStNameTr() {
        return stNameTr;
    }

    public void setStNameTr(String stNameTr) {
        this.stNameTr = stNameTr;
    }
    public String getStBaseTr() {
        return stBaseTr;
    }

    public void setStBaseTr(String stBaseTr) {
        this.stBaseTr = stBaseTr;
    }
    public String getTrStrAft() {
        return trStrAft;
    }

    public void setTrStrAft(String trStrAft) {
        this.trStrAft = trStrAft;
    }
    public String getTrStrAtt() {
        return trStrAtt;
    }

    public void setTrStrAtt(String trStrAtt) {
        this.trStrAtt = trStrAtt;
    }

    @Override
    protected Serializable pkVal() {
        return null;
    }

    @Override
    public String toString() {
        return "Streettrans{" +
            "featureId=" + featureId +
            ", transType=" + transType +
            ", stNameTr=" + stNameTr +
            ", stBaseTr=" + stBaseTr +
            ", trStrAft=" + trStrAft +
            ", trStrAtt=" + trStrAtt +
        "}";
    }
}
